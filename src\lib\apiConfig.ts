export const views = {
  course: "v_course",
  users: "v_person_with_orgs",
  attendExam: "v_all_quiz_by_enrolled_users",
  questionBank: "v_question_bank",
  addExams: "v_course_details",
  submittedAnswers: "v_submit_answers",
  courseDetails: "v_course_details",
  currentAffairsList: "v_view_bulletin_board",
  checkPointQuiz: "v_checkpoint_quiz",
  sectionList: "v_course_details",
  signUpUser: "signup",
  organizationList: "v_user_orgs",
  sessionViewsReport: "get_checkpoint_sessions_report",
  dashboardStatsCount: "get_dashboard_entity_counts",
  mostWatcchedUsers: "get_dashboard_most_progressed_users",
  checkpointsProgress: "get_dashboard_checkpoint_progress",
  checkpointsCount: "get_video_checkpoint_count",
  groups: "v_group",
  assignUsersList: "v_users_without_org",
  userOrganizationList: "v_user_orgs",
  privileges: "v_all_privileges",
  courseWiseStatistics: "get_dashboard_video_watched_count",
  userWiseStatistics: "get_dashboard_video_session_stats",
  checkPointsList: "get_check_point_by_course_module_id",
  profileImage: "v_person",
  subscriptionList: "v_subscription_plan",
  moduleList: "v_module_list",
  folderList: "v_course_folders",
  latestEnrollments: "v_latest_enrollment",
};

export const rpc = {
  subscribedUsersList: "get_user_list_by_plan",
  signUp: process.env.NEXT_PUBLIC_SUPABASE_URL + "/auth/v1/signup",
  // signUp: "https://tsghdsndkborjlbxzfyb.supabase.co/auth/v1/signup",
  // resourceLibrary: "fn_fetch_resources_library_items",
  resourceLibrary: "fn_fetch_resources_library_items",
  resourceLibraryLinked: "fn_link_resource_library_items_to_course",
  resourceLibraryNotLinked: "fn_get_resources_library_not_linked_to_course",
  subscriptionList: "get_subscription_plan_lists_for_admin",
  deleteResourceLinked: "delete_resource_url",
  getCoursesLinkedResourcesLibrary: "get_courses_linked_resources_library",
  addResourceFile: "fn_add_resource_file",
  addResourceUrl: "fn_add_resource_url",
  addResourcePage: "fn_add_resource_page ",
  deleteResourceLibraryItem: "delete_resource_library_item",
  editResourceFile: "fn_edit_resource_file",
  editResourceUrl: "fn_edit_resource_url",
  editResourcePage: "fn_edit_resource_page",
  publishResourceItem: "publish_resource_item",
  getCourseDetails: "get_course_details",
  insertBulletinBoard: "insert_bulletin_board",
  notEnrolledUsers: "not_enrolled_users",
  addEnrollment: "add_enrollment",
  insertQuiz: "fn_insert_quiz",
  updateQuiz: "fn_edit_quiz",
  getRolesOfOrg: "get_roles_of_org",
  insertCourse: "insert_course",
  copyCourseDetails: "fn_duplicate_course_custom",
  getSectionDetails: "get_section_details",
  updateCourse: "fn_update_course",
  deleteCourse: "fn_delete_course",
  addCheckPoint: "add_check_point",
  insertPage: "insert_page",
  addCourseResource: "add_course_resource",
  publishCourse: "publish_course",
  updateCheckpointStatus: "update_checkpoint_status",
  getCommentsOfInstance: "fn_get_comments_of_instance",
  deleteCourseLinkedResourcesLibrary: "delete_course_linked_resources_library",
  enrolledUsers: "fn_get_enrolled_users",
  getQuestionsOfQuiz: "fn_fetch_questions_of_quiz",
  startDryRunQuiz: "start_dry_run_quiz",
  submitQuizAnswers: "submit_quiz_answers",
  getQuizRightAnswers: "get_quiz_right_answers",
  cleanupDryRunQuizes: "cleanup_dry_run_quizes",
  getQuizesOfCourse: "get_quizes_of_course",
  publishExam: "publish_exam",
  unenrollUser: "unenroll_user",
  insertGroup: "insert_group",
  updateGroup: "update_group",
  deleteGroup: "delete_group",
  getUsersListForGroup: "get_users_list_for_group",
  updateUsersForGroup: "update_users_for_group",
  getCourseListForGroup: "get_course_list_for_group",
  updateCoursesForGroup: "update_courses_for_group",
  getPrivilegesListForGroup: "get_privileges_list_for_group",
  updatePrivilegesForGroup: "update_privileges_for_group",
  insertQuestionsFromBank: "fn_insert_questions_from_bank",
  getPrivilegeListForRole: "get_privilege_list_for_role",
  updateRolePrivilege: "fn_insert_role_privileges",
  getUserRolePrivilege: "get_user_role_privilege",
  getQuesCategoryHierarchy: "get_ques_category_hierarchy",
  insertQuizQuestion: "insert_quiz_question",
  insertQuestionCategory: "insert_question_category",
  addSubscriptionPlan: "add_subscription_plan",
  deleteSubscriptionPlan: "delete_subscription_plan",
  approveSubscriptionPlan: "publish_subscription_plan",
  updateSubscriptionPlan: "update_subscription_plan",
  getCourseListForPlanMapping: "fn_get_course_list_for_plan_mapping",
  updateCoursesForPlan: "fn_update_courses_for_plan",
  getResourceListForPlanMapping: "get_resource_list_for_plan_mapping",
  updateResourcesForPlan: "update_resources_for_plan",
  getCategoryHierarchy: "get_category_hierarchy",
  insertCourseCategory: "insert_course_category",
  updateCategory: "update_category",
  deleteCategory: "delete_category",
  publishCategory: "publish_category",
  addOrgClaimRoleToUsers: "add_org_claim_role_to_users",
  blockUser: "set_user_block_status",
  updateQuestionCategory: "update_question_category",
  deleteQuestionCategory: "delete_question_category",
  activityLog: "fn_fetch_user_activities",
  // getCourseVideoProgressList: "get_course_video_progress_list",
  // getCourseVideoProgressList: "fn_get_course_video_progress_resource_list",
  getCourseVideoProgressList: "fn_get_course_video_progress_resource_list",
  updateQuizQuestion: "update_quiz_question",
  publishQuestionCategory: "publish_question_category",
  publishQuestion: "publish_question",
  publishAffair: "publish_bulletin",
  deleteQuestionFromQuiz: "fn_delete_question_from_quiz",
  publishQuestions: "publish_questions",
  getConfigurations: "get_configurations",
  updateBulletin: "update_bulletin",
  deleteBulletin: "delete_bulletin",
  deleteUserSubscription: "remove_user_from_subscription_plan ",
  addUsersToSubscription: "add_subscription_plan_for_user_by_admin",
  fetDeviceToken: "fetch_device_token",
  pushNotification: "push_notification",
  insertMessage: "insert_notification_content",
  courseCompletionProgress: "fn_fetch_course_completion_progress",
  getPlanListForCourse: "get_plan_list_for_course",
  getEnrolledCourses: "fn_fetch_enrolled_courses_user",
  getCoursesByCategory: "fn_fetch_courses_by_category_with_subcategories",
  getReportingToResource: "fn_get_user_reporting_heirarchy",
  getNotEnrolledUsersList: "fn_not_approved_users",
  courseCompletedUsers: "fn_get_course_completion_summary_dtls",
  getFoldersList: "fn_get_resource_folder",
  createFolder: "fn_create_resource_folder",
  deleteSection: "fn_delete_course_section",
  getCommentsForApproval: "fn_fetch_comments_for_approval",
  updateCommentStatus: "fn_update_comment_status",
  getFolderResource: "fn_get_folder_resources",
  importFolder: "fn_import_folder_resources_to_course",
  extendPlanValidity: "fn_extend_membership_plan",
  getCourseList: "fn_get_course_list_for_resource_mapping",
  mapResourceToCourse: "fn_import_resources_to_courses",
  updateResourceUrl: "fn_replace_resource_content",
  deleteLinkedResources: "fn_delete_linked_resource_from_course",
  insertQuizQuestionsExcelArray: "fn_import_questions_from_excel",
  addCustomBranidng: "fn_add_custom_branding_details",
  getCustomBrandingDetails: "fn_get_custom_branding_details",
  updateDashboardConfig: "fn_update_app_dashboard_config",
  removeDashboardResource: "fn_remove_dashboard_resource",
  getDashboardData: "fn_get_app_dashboard_config",
  getCourseResourceDetails: "fn_get_course_resources_details",
  insertLiveClassDetails: "fn_upload_live_class_details",
  addComments: "fn_add_comment",
  getCourseListForExamMapping: "fn_get_course_list_for_exam_mapping",
  insertQuizToCourses: "fn_import_quiz_to_courses",
  logUserActivity: "fn_record_user_activity",
  importExam: "fn_create_exam_to_resource_library",
  addQuiz: "fn_create_quiz",
  getQuestionsOfQuizFn: "fn_view_resource_library_quiz",
  importQuestionsFromBank: "fn_insert_questions_from_bank",
  addCourseSection: "fn_add_course_section",
  manageFolder: "fn_manage_folder_dtls",
  orderSection: "fn_reorder_sections",
  orderFolder: "fn_reorder_folders",
  ordeResource: "fn_reorder_resources",
  getCoursePurchaseRequest: "fn_get_course_purchase_requests",
  updatePurchaseRequest: "fn_update_purchase_request_status",
  getLiveClassDetails: "fn_get_live_class_details",
  updateLiveClassDetails: "ms_fn_edit_live_class_details",
};

export const functions = {
  pushNotification:
    process.env.NEXT_PUBLIC_SUPABASE_URL + "/functions/v1/push_notification",
  sendEmailNotification:
    process.env.NEXT_PUBLIC_SUPABASE_URL +
    "/functions/v1/send_email_notification",

  sendEmailWithAttachment:
    process.env.NEXT_PUBLIC_SUPABASE_URL +
    "/functions/v1/send_mail_with_attachment",

  inviteUserToWebapp:
    process.env.NEXT_PUBLIC_SUPABASE_URL +
    "/functions/v1/invite_user_to_webapp",
};
