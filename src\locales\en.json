{"dashboard": {"statusCounts": {"courses": "Courses", "videos": "Videos", "exams": "<PERSON><PERSON>", "practice": "Practice"}, "youTubeVideos": {"title": "YouTube Videos", "filterByName": "Filter by Video name", "view": "View", "videoName": "Video Name", "createdOn": "Created On", "sessionDetails": "Session Details", "slno": "Sl. No.", "name": "Name", "attendedExam": "Attended Exam", "sessionEnd": "Session End At", "result": "Result"}, "currentaffairs": {"title": "Current Affairs", "filterByEventTitle": "Filter by Event Title", "content": "Content", "eventTitle": "Event title", "publishedDate": "Published date"}, "courseWiseStatistics": {"title": "Course Wise Statistics", "userCount": "User Count", "enrolled": "Enrolled", "watched": "Watched", "notWatched": "Not watched", "passed": "Passed", "failed": "Failed", "inProgress": "In progress"}, "newRegistrations": {"title": "New Registrations", "filterByName": "Filter by name", "enroll": "Enroll", "name": "Name", "email": "Email", "profile": "Profile", "phoneNumber": "Phone Number"}, "recentEnrollments": {"title": "Recent Enrollments", "filterByName": "Filter by name", "name": "Name", "enrolledDate": "Enrolled Date", "course": "Course", "validUpTo": "Valid Up To"}, "users": {"title": "Users", "filterByName": "Filter by First name", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "profile": "Profile", "phone": "Phone", "role": "Role"}, "courseBasedStatistics": "Course based statistics", "userBasedStatistics": "User based statistics", "viewAll": " View All", "loadingStatistics": "Loading statistics...", "noDataAvailable": "No data available", "userSessionReport": "User Session Report", "courseResources": "Course Resources", "courseSelected": "Course Selected", "user": "User", "exportExcel": "Export Excel", "selectCourse": "Select Course", "selectUser": "Select a User:", "resources": "Resources", "courseName": "Course Name", "checkpointsViewInprogress": "Checkpoints view in progress", "videoName": "Video Name", "checkpointProgressCount": "Checkpoint progress/count", "resourceProgress": "Resource Progress", "courseStatisticsCompletedUsers": "Course Statistics - 100% Completed Users", "noResultsFound": "No results found", "courseProgressedUsers": "Course Progressed Users", "checkpointViews": "Checkpoint Views", "selectVideo": "Select Video", "sessionDetailAllAttempts": "Session Details[All Attempts]", "filterByName": "Filter by name", "view": "View", "certificate": "Certificate", "name": "Name", "profile": "Profile", "email": "Email", "enrolledCourse": "Enrolled Course", "progress": "Progress", "footer": {"copyright": "Copyright ©2025 ", "rightsReserved": ". All rights reserved."}}, "organization": {"organization": "Organization"}, "customDashboard": {"dashboardCustomization": "Dashboard Customization", "currentAffairs": "Current Affairs", "noOfItemsToDisplay": "No. of items to be display", "generalResources": "General Resources", "assignments": "Assignments", "subjects": "Subjects", "exams": "<PERSON><PERSON>", "true": "True", "false": "False", "addResource": "Add Resource", "editResource": "Edit Resource", "deleteResource": "Delete Resource", "resourceEdit": {"selectFilter": "Select a filter", "filterByFolderAndResourceType": "Filter by Folder & Resource Type", "selectFolder": "Select Folder", "selectResourceType": "Select Resource Type", "filterByCourseAndSection": "Filter by Course & Section", "selectCourse": "Select Course", "selectSection": "Select Section"}, "resourceDelete": {"deletePrompt": "Do you want to delete this resource?"}}, "topics": {"title": "Category", "addNewTopic": "Add new Topic", "searchByName": "Search by name...", "search": "Search", "clear": "Clear", "name": "Name", "description": "Description", "add": "Add", "edit": "Edit", "delete": "Delete", "status": "Published Status", "premium": "Premium", "addCategory": "Add Category", "updateCategory": "Update Category", "parentCategoryInfo": "Parent category Information", "deleteCategory": "Delete Category", "categoryStatus": "Category Status", "categoryName": "Category Name", "isPremium": "Is Premium", "deletePrompt": "Do you want to delete this category ?", "datftPrompt": "Do you want to move this category to Draft ?", "publishPrompt": "Do you want to Publish this category ?"}, "courses": {"title": "Courses", "addCourse": "Add Course", "updateCourse": "Update Course", "addNewCourse": "Add New Course", "selectCategory": "Select Category", "selectItem": "Select Item", "searchByShortName": "Search by Short name", "searchByName": "Search by name...", "searchByPublishedStatus": "Search by Published Status", "paid": "Paid", "free": "Free", "demo": "Demo", "from": "From", "to": "To", "premium": "Premium", "expired": "Expired", "duplicate": "Duplicate", "duplicateCourse": "Duplicate Course", "publishedDraftCourse": "Published/Draft Course", "courseFullName": "Course Full Name", "courseShortName": "Course Short Name", "courseCategory": "Course Category", "courseStartDate": "Course Start Date", "courseEndDate": "Course End Date", "selectCourseType": "Select course type", "courseVisibility": "Course Visibility", "coursePublishPrompt": "Do you want to Publish the course ?", "courseDraftPrompt": "Do you want to move the course to Draft ?", "yes": "Yes", "no": "No", "format": "Format", "noOfSections": "Number Of Sections", "description": "Description", "courseDetails": "Course Details", "modules": "<PERSON><PERSON><PERSON>", "participants": "Participants", "membershipPlans": "Membership Plans", "duration": "Duration", "filterByFirstName": "Filter by First name", "filterByPlanName": "Filter by Plan name", "firstName": "First Name", "lastName": "Last Name", "enrolledDate": "Enrolled Date", "courseDescription": "Course Description", "deletePrompt": "Do you want to delete this Course ?", "courseModule": {"fromDate": "From Date", "toDate": "To Date", "noOfQuestions": "No. of questions", "totalMarks": "Total Marks", "passMarks": "Pass Marks", "checkpoints": "Checkpoints", "comments": "Comments", "warningText": "Warning: These changes will be saved only after submission", "filterByName": "Filter by Name", "addResource": "Add Resource", "checkpointDetails": "Checkpoint Details", "importResource": "Import Resource", "deleteResource": "Delete Resource", "addFolder": "Add Folder", "importFolder": "Import Folder", "deleteSection": "Delete Section", "deletePrompt": "Do you want to delete this section ?", "selectResource": "Select Resource", "name": "Name", "url": "URL", "milestonePresent": "Milestone Present?", "numberOfCheckpoints": "Number of Checkpoints", "randomEnabled": "Random Enabled?", "alwaysShowCheckpoint": "Always show Checkpoint?", "sequenceNumber": "Sequence Number", "checkpointName": "Checkpoint Name", "checkpointStartTime": "Checkpoint Start Time", "checkpointType": "Checkpoint Type", "checkpointResourceId": "Checkpoint Resource Id", "isMandatory": "Is Mandatory?", "selectResourceType": "Select Resource Type", "selectFolder": "Select Folder", "isPremium": "Is Premium", "slno": "Sl. No.", "resourceName": "Resource Name", "createdOn": "Created On", "order": "Order", "folderName": "Folder Name", "moduleType": "Module Type", "import": "Import", "checkpointSlide": "Checkpoint Slide", "seqNo": " Seq. No", "startTime": "Start Time", "mandatory": "Mandatory", "slideNo": "Slide Number", "addCourseVideo": "Add Course Video", "filterByResourceName": "Filter by resource name", "resources": "Resources", "days": "Days", "from": "From", "courseDetails": "Course Details", "pasteLink": "Paste Video Link here", "pasteUrl": "Paste URL...", "selectCategoryType": "Select category type", "selectTopicCategory": "Select topic [category]", "selectTopic": "Select Topic", "categoryName": "Category Name", "selectCheckpointResourceId": "Select checkpoint Resource Id", "filterByCheckpointName": "Filter by checkpoint name", "checkpointWarning": " Warning: These changes will be saved only after submission."}, "section": {"addSection": "Add Section", "sectionName": "Section Name", "order": "Order", "name": "Name", "remove": "Remove", "submitAll": "Submit All"}}, "dataTable": {"slno": "Sl. No.", "noDataFound": "No data found.", "rowsPerPage": "Rows per page", "pageInfo": "Page {{current}} of {{total}}"}, "buttons": {"cancel": "Cancel", "submit": "Submit", "close": "Close", "addResource": "+ Add Resource", "update": "Update", "delete": "Delete", "search": "Search", "clear": "Clear", "draft": "Draft", "published": "Published", "duplicate": "Duplicate", "view": "View", "edit": "Edit", "showMoreDetails": "Show more details", "addCheckpoint": "Add Checkpoint", "importResource": "Import Resource", "importFolder": "Import Folder", "addFolder": "Add Folder", "deleteResources": "Delete Resources", "checkpointDetails": "Checkpoint details", "confirm": "Confirm", "addToGrid": "Add to grid", "publish": "Publish", "remove": "Remove", "addEquation": "Add Equation", "send": "Send"}, "questionCategory": {"title": "Question Category", "addQuestionCategory": "Question Category", "editQuestionCategory": "Edit Question Category", "addNewQuestionCategory": "Add new Question Category", "categoryName": "Category Name", "description": "Description", "filterByCategoryName": "Filter by Category name", "edit": "Edit", "delete": "Delete", "status": "Status", "deletePrompt": "Do you want to delete the Question Category?", "draftCategory": "Draft Category", "publishCategory": "Publish Category", "draftPrompt": " Do you want to move this category to Draft?", "publishPrompt": "Do you want to Publish this category?"}, "attendedExams": {"title": "Attended Exams", "selectCourse": "Select course", "selectUser": "Select user", "filterByUserName": "Filter by User name", "view": "View", "userAnswerSheet": "User Answer Sheet", "correct": "Correct", "wrong": "Wrong", "errorFetchingData": "Error fetching data", "columns": {"course": "Course", "firstName": "First Name", "lastName": "Last Name", "examName": "Exam name", "examType": "Exam type", "duration": "Duration", "submittedOn": "Submitted on", "passmark": "Passmark", "status": "Status", "marksObtained": "Marks obtained", "result": "Result"}, "status": {"evaluated": "evaluated", "pending": "pending"}, "result": {"passed": "Passed", "failed": "Failed"}}, "usersList": {"title": "Users", "addUser": "Add user", "selectRole": "Select role", "status": "Status", "filterByFirstName": "Filter by First name", "changeRole": "Change Role", "enrolledCourses": "Enrolled Courses", "addNewUser": "Add New User", "userEnrolledCourses": "User Enrolled Courses", "errorFetchingData": "Error fetching data", "columns": {"firstName": "First Name", "lastName": "Last Name", "email": "Email", "profile": "Profile", "phone": "Phone", "role": "Role"}, "changeUserRole": {"title": "Change User Role", "selectNewRole": "Select New Role", "currentRole": "Current Role", "newRole": "New Role", "privileges": "Privileges", "cancel": "Cancel", "update": "Update", "roleUpdatedSuccessfully": "Role updated successfully", "errorUpdatingRole": "Error updating role"}, "enrolledCoursesModal": {"title": "Enrolled Courses", "courseName": "Course Name", "enrolledDate": "Enrolled Date", "validUpTo": "Valid Up To", "status": "Status", "close": "Close"}}, "groups": {"teams": {"title": "Teams", "addGroup": "Add Group", "editGroup": "Edit Group", "deleteGroup": "Delete Group", "filterByGroup": "Filter by Group", "users": "Users", "courses": "Courses", "privileges": "Privileges", "columns": {"groupName": "Group Name", "description": "Description", "createdDate": "Created Date"}, "addGroupModal": {"title": "Add Group", "editTitle": "Edit Group", "groupName": "Group Name", "description": "Description", "cancel": "Cancel", "save": "Save", "update": "Update", "groupNameRequired": "Group name is required", "descriptionRequired": "Description is required", "groupAddedSuccessfully": "Group added successfully", "groupUpdatedSuccessfully": "Group updated successfully", "errorAddingGroup": "Error adding group", "errorUpdatingGroup": "Error updating group"}, "deleteGroupModal": {"title": "Delete Group", "confirmMessage": "Are you sure you want to delete this group?", "cancel": "Cancel", "delete": "Delete", "groupDeletedSuccessfully": "Group deleted successfully", "errorDeletingGroup": "Error deleting group"}}, "userGroup": {"user": "User", "title": "User Group", "selectGroup": "Select Group", "filterByUserName": "Filter by User name", "addUsersToGroup": "Add Users to Group", "removeUsersFromGroup": "Remove Users from Group", "update": "Update", "cancel": "Cancel", "noUsersSelected": "Please select at least one user", "usersUpdatedSuccessfully": "Users updated successfully", "errorUpdatingUsers": "Error updating users", "columns": {"userName": "User Name", "email": "Email", "isPartOfGroup": "Part of Group"}}, "courseGroup": {"title": "Course Group", "selectGroup": "Select Group", "filterByCourseName": "Filter by Course name", "filterByCourse": "Filter by course", "addCoursesToGroup": "Add Courses to Group", "removeCoursesFromGroup": "Remove Courses from Group", "update": "Update", "cancel": "Cancel", "noCoursesSelected": "Please select at least one course", "coursesUpdatedSuccessfully": "Courses updated successfully", "errorUpdatingCourses": "Error updating courses", "groupsUpdated": "Groups updated", "successfullyUpdatedGroups": "Successfully updated groups", "errorFetchingGroups": "Error fetching groups", "columns": {"courseName": "Course Name", "course": "Course", "courseType": "Course Type", "isPartOfGroup": "Part of Group"}}, "groupPrivileges": {"title": "Privilege Group", "selectGroup": "Select Group", "filterByPrivilegeName": "Filter by Privilege name", "filterByKey": "Filter by Key", "addPrivilegesToGroup": "Add Privileges to Group", "removePrivilegesFromGroup": "Remove Privileges from Group", "update": "Update", "cancel": "Cancel", "noPrivilegesSelected": "Please select at least one privilege", "privilegesUpdatedSuccessfully": "Privileges updated successfully", "errorUpdatingPrivileges": "Error updating privileges", "groupsUpdated": "Groups updated", "successfullyUpdatedGroups": "Successfully updated groups", "errorUpdatingGroups": "Error updating groups", "errorFetchingGroups": "Error fetching groups", "columns": {"privilegeName": "Privilege Name", "name": "Name", "key": "Key", "description": "Description", "screen": "Screen", "isPartOfGroup": "Part of Group"}}}, "questionBank": {"title": "Question Bank", "slno": "Sl. No.", "question": "Question", "category": "Category", "defaultMark": "De<PERSON><PERSON>", "penaltyScore": "Penalty Score", "createQuestion": "Create Question", "publishQuestionInBatch": "Publish Question in Batch", "selectQuestionCategory": "Select Question Category", "importQuestions": "Import Questions", "editResource": "Export to Excel", "exportToPDF": "Export to PDF", "filterByQuestion": "Filter by Question", "edit": "Edit", "view": "View", "publishedStatus": "Published Status", "questionCategory": "Question Category", "qefaultMark": "De<PERSON><PERSON>", "status": "Status", "addQuestionCategory": "Add Question Category", "draftQuestion": "Draft Question", "publishQuestion": "Publish Question", "draftPrompt": "Do you want to move this Question to Draft?", "publishPrompt": "Do you want to Publish this Question?", "publishQuestions": "Publish Questions", "selectFile": "Select File", "questionHint": "Question Hint", "option": "Option", "rightAnswer": "Right Answer", "solution": "Solution", "bestSolution": "Best Solution", "typeOfQuestions": "Type of Questions", "type": "Type", "editEquations": "Edit Equations", "addEquations": "Add Equations", "enterTheQuestion": "Enter the question", "markAssigned": "<PERSON>", "penaltyApplicable": "Penality, if applicable", "choice": "Choice", "markAsAnswer": "<PERSON> as Answer", "fraction": "Fraction", "select": "Select", "addQuestion": " Add Question"}, "combobox": {"selectItem": "Select Item", "selectOrganization": "Select Organization"}, "confirmationModal": {"submitWithoutData": "Do you want to submit without {{data}}?"}, "pagination": {"previous": "Previous", "next": "Next"}, "enrollment": {"title": "Enrollment", "newEnrollment": "New Enrollment", "selectCourse": "Select Course", "filterByEmail": "Filter by <PERSON><PERSON>", "exportToExcel": "Export to Excel", "filterByName": "Filter by name", "remove": "Remove", "sendEmail": "Send Email", "removeUser": "Remove User", "deletePrompt": "You want to remove the user from this course?", "composeMailContent": "Compose mail content", "message": "Message", "subject": "Subject", "messageBody": "Message Body", "name": "Name", "email": "Email", "enrolledDate": "Enrolled Date", "addEnrollments": "Add Enrollments", "filterByFirstName": "Filter by First name", "firstName": "First Name", "lastName": "Last Name"}, "membershipPlan": {"planName": "Plan Name", "frequency": "Frequency", "type": "Type", "price": "Price", "currency": "<PERSON><PERSON><PERSON><PERSON>", "validFrom": "<PERSON><PERSON>", "validTo": "<PERSON><PERSON>", "addUsers": {"title": "Add Users", "filterLabel": "Filter by First name", "columns": {"firstName": "First name", "lastName": "Last Name", "email": "Email"}, "buttons": {"cancel": "Cancel", "submit": "Submit"}, "messages": {"error": {"title": "Error", "description": "Error fetching data"}}}}, "currentAffairs": {"title": "Current Affairs", "addCurrentAffair": "Add Current Affairs", "selectMonth": "Select Month", "filterByEventTitle": "Filter by Event Title", "viewContent": "View Content", "edit": "Edit", "delete": "Delete", "publish": "Publish", "eventTitle": "Event title", "publishedDate": "Published date", "publishCurrentAffairs": "Publish Current Affairs", "unpublishEvent": "Unpublish Event", "deleteEvent": "Delete Event", "publishPrompt": "Do you want to Publish this category ?", "selectCourse": "Select Course", "unpublishPrompt": "Do you want to Unpublish this event ?", "updateCurrentAffairs": "Update Current Affairs", "description": "Description"}, "assignOrganization": {"usersList": "Users List", "assignOrganization": "Assign Organization", "filterByFirstName": "Filter by First name", "selectOrganization": "Select Organization", "selectRole": "Select Role", "firstName": "First Name", "lastName": "Last Name", "email": "Email"}, "accessPrivileges": {"title": "Access Privileges", "selectRole": "Select Role", "filterByPrivilegeName": "Filter by Privilege name", "name": "Name", "key": "Key", "description": "Description"}, "subscriptionPlan": {"publishSubscription": "Publish Subscription", "draftSubscription": "Draft Subscription", "title": "Subscription Plans", "addSubscription": "Create Subscription Plan", "approveSubscriptionRequest": "Approve Subscription Request", "filterByPlan": "Filter by Plan", "viewLinkCourseResources": "View/Link Course/Resources", "user": "User", "edit": "Edit", "delete": "Delete", "extendValidity": "Extend Validity", "publishedStatus": "Published Status", "addUser": "Add User", "addSubscriptionPlan": "Add Subscription Plan", "editSubscriptionPlan": "Edit Subscription Plan", "deleteSubscriptionPlan": "Delete Subscription Plan", "userMembership": "User Membership", "approveSubscription": "Approve Subscription", "updatePlanExpiry": "Update plan expiry", "planName": "Plan Name", "description": "Description", "type": "Type", "price": "Price", "currency": "<PERSON><PERSON><PERSON><PERSON>", "validFrom": "<PERSON><PERSON>", "validTo": "<PERSON><PERSON>", "status": "Status", "expiryStatus": "Expiry Status", "deletePrompt": "Do you want to delete the Subscription ?", "draftPrompt": "Do you want to move this exam to Draft ?", "publishPrompt": "Do you want to Publish this Plan ?", "filterByUser": "Filter by User", "deleteUser": "Delete User", "name": "Name", "plan": "Plan", "purchaseDate": "Purchase Date", "validUpTo": "Valid Up To", "paymentMethod": "Payment Method", "removeUserPrompt": "Do you want to remove the Subscription User ?", "selectPlan": "Select Plan", "filterByUserName": "Filter by user name", "approve": "Approve", "userName": "User Name", "email": "Email", "amount": "Amount", "filterByFirstName": "Filter by First name", "firstName": "First Name", "lastName": "Last Name", "membershipName": "Membership Name", "basedOn": "Based on", "paymentFrequency": "Payment frequency", "updateToSubscription": "Update {{title_variable}} to the Subscription", "planSelected": "Plan Selected", "courses": "Courses", "resource": "Resources", "time": "Time", "courseSelected": "Course Selected", "filterByCourseName": "Filter by Course name", "courseName": "Course Name", "resourceName": "Resource Name", "filterByResourceName": "Filter by Resource name", "noDataFound": "No data found."}, "resourceLibrary": {"title": "Resources", "addNewResource": "Add New Resource", "addNewExam": "Add New Exam", "selectFolder": "Select Folder", "filterByResourceName": "Filter by Resource name", "quizName": "Quiz Name", "quizType": "Quiz Type", "createdOn": "Created On", "duration": "Duration", "noOfQuestions": "No. of Questions", "totalMark": "Total Mark", "passMark": "Pass Mark", "penalty": "Penalty ?", "linkToCourse": "Link To Course", "resourceName": "Resource Name", "resourceType": "Resource Type", "folderName": "Folder Name", "viewLinkedCourse": "View Linked Course", "edit": "Edit", "delete": "Delete", "changeUrl": "Change URL", "publishedStatus": "Published Status", "coursesLinkedToResource": "Courses linked to the Resource", "courseName": "Course Name", "sectionName": "Section Name", "filterByCourseName": "Filter by Course name", "back": "Back", "linkResourceToCourse": "Link Resource to Course", "searchCourse": "Search course...", "selectSection": "Select Section", "noCoursesAvailable": "No courses available", "save": "Save", "addResource": "Add Resource", "uploadType": "Upload Type", "addResourceType": "Add - {{type}}", "editResourceType": "Edit - {{type}}", "addModule": "<PERSON><PERSON>", "file": "File", "video": "Video", "addFolder": "Add Folder", "name": "Name", "extension": "Extension", "url": "URL", "pasteUrl": "Paste URL...", "totalSlideCount": "Total Slide Count", "pageCount": "Page Count", "videoLength": "Video Length", "hours": "Hours", "minutes": "Minutes", "seconds": "Seconds", "description": "Description", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "validFrom": "<PERSON><PERSON>", "validTo": "<PERSON><PERSON>", "pdfUpload": "PDF Upload", "uploadPdfFile": "Upload PDF File", "addPage": "Add - Page", "editPage": "Edit - Page", "pageTitle": "Page Title", "pageContent": "Page Content", "uploadResource": "Upload Resource", "upload": "Upload", "updateUrl": "Update URL", "updatePageContent": "Update Page Content", "deletePrompt": "Do you want to delete this resource ?", "draftPrompt": "Do you want to move this resource to Draft ?", "publishPrompt": "Do you want to Publish this resource ?", "deleteResource": "Delete Resource", "updateResourceStatus": "Update Resource Status", "selectCategory": "Select Category", "selectExamType": "Select Exam Type", "examName": "Exam Name", "totalMarks": "Total Marks", "passMarks": "Pass Marks", "examDuration": "Duration (in minutes)", "allowedAttempts": "Allowed Attempts", "penaltyApplicable": "Penalty applicable ?", "penaltyType": "Penalty Type", "selectPenaltyType": "Select Penalty Type", "questionBased": "Question based", "examBased": "Exam based", "penaltyMode": "Penalty Mode", "selectPenaltyMode": "Select Penalty Mode", "fixed": "Fixed", "percentageWise": "Percentage Wise", "noOfWrongAnswers": "No. of Wrong Answers", "penalty%": "Penalty (%)", "penaltyMarksDeducted": "Penalty (marks deducted)", "premiumContent": "Premium Content", "milestonePresent": "Milestone Present?", "numberOfCheckpoints": "Number of Checkpoints", "randomEnabled": "Random Enabled?", "alwaysShowCheckpoint": "Always show Checkpoint?", "isEqualWeightage": "Does each question carry equal weightage ?", "marks": "Marks", "examRules": "Exam rules/particulars", "linkExamToCourse": "Link <PERSON>am to Course", "folderPrompt": "Choose a folder if you wish to add the exam to a specific one", "add_page": "Add Page", "selectCourse": "Select Course", "actions": "Actions", "filterByFolderName": "Filter by Folder Name"}, "sideMenu": {"dashboard": "Dashboard", "organization": "Organization", "customDashboard": "Custom Dashboard", "category": "Category", "courses": "Courses", "enrollments": "Enrollments", "exams": "<PERSON><PERSON>", "questionCategory": "Question Category", "questionBank": "Question Bank", "attendedExams": "Attended Exams", "users": "Users", "groups": "Groups", "currentAffairs": "Current Affairs", "assignOrganization": "Assign Organization", "accessPrivileges": "Access Privileges", "subscriptionPlans": "Subscription Plans", "resourceLibrary": "Resource Library", "youtubeVideos": "YouTube Videos", "commentsFeedback": "Comments & Feedback", "ScheduleClass": "Schedule Live Class"}, "navMenu": {"profile": "Profile", "theme": "Theme", "commentsFeedback": "Comments and Feedback", "scheduleMeeting": "Schedule Meeting", "activityLog": "Activity Log", "logout": "Logout"}, "comments": {"title": "Comments and Feedback", "searchName": "Search by name...", "message": "Message", "pendingReply": "Pending Reply", "messageType": "Message Type", "subject": "Subject", "userName": "User Name", "profile": "Profile", "createdDate": "Created Date", "resourceName": "Resource Name", "status": "Status", "approveReject": "Approve/Reject", "reply": "Reply", "approveRejectComments": "Approve/Reject Comments", "comment": "Comment", "action": "Action", "approve": "Approve", "reject": "Reject", "approvePrompt": "Would you like to approve this comment?", "rejectPrompt": "Would you like to reject this comment?", "replyMessage": "Reply Message", "replyMessageFor": "Enter reply message for", "typeHere": "Type here..."}, "config": {"title": "Configuration Settings", "theme": "Theme", "selectTheme": "Select a theme", "validFrom": "<PERSON><PERSON>", "validTo": "<PERSON><PERSON>"}, "meeting": {"title": "Schedule a Live Class Session", "meetingDetails": "Meeting Details", "selectCourse": "Select Course", "selectMeetingPlatform": "Select Meeting Platform", "accessInfo": "Meeting Access Information (Provide either URL OR ID )", "meetingUrl": "Meeting URL", "meetingId": "Meeting ID", "passcode": "Passcode", "schedule": "Schedule", "startDateTime": "Start Date & Time", "endDateTime": "End Date & Time", "LiveClasses": "Live Classes", "ScheduleClass": "Schedule Live Class", "Search": "Search Meeting", "platform": "Platform", "status": "Status", "AddLiveClass": "Add Live Class", "selectPlatform": "Select Platform", "editMeeting": "Edit Meeting", "meetingUpdated": "Meeting Updated", "meetingUpdatedSuccessfully": "Meeting updated successfully", "cannotEditPastMeeting": "Cannot edit past meetings", "allStatuses": "All Statuses", "upcoming": "Upcoming", "active": "Active", "expired": "Expired", "selectStatus": "Select Status", "enterPasscode": "Enter Passcode"}, "activityLog": {"title": "Activity Log", "userSelected": "User Selected", "filterByModule": "Filter by <PERSON><PERSON><PERSON>", "screen": "Screen", "comment": "Comment", "activityStatus": "Activity Status", "userAgent": "User Agent", "date": "Date"}, "breadcrumb": {"dashboard": "Dashboard", "editCourse": "Edit Course", "courses": "Courses", "course": "Course", "viewCourse": "View Course", "updateCourse": "Update Course", "organization": "Organization", "topics": "Topics", "category": "Category", "enrollments": "Enrollments", "addEnrollments": "Add Enrollments", "exams": "<PERSON><PERSON>", "addExams": "<PERSON>d <PERSON>", "questionCategory": "Question Category", "questionBank": "Question Bank", "addQuestion": " Add Question", "attendedExams": "Attended Exams", "users": "Users", "examDetails": "<PERSON><PERSON>", "groups": "Groups", "userGroup": "User Group", "courseGroup": "Course Group", "privilegeGroup": "Privilege Group", "accessPrivileges": "Access Privileges", "currentAffairs": "Current Affairs", "addCurrentAffairs": "Add Current Affairs", "subscriptionPlans": "Subscription Plans", "addSubscriptionPlan": "Add Subscription Plan", "editSubscriptionPlan": "Edit Subscription Plan", "updateSubscriptionPlan": "Update Resource to the Subscription", "resources": "Resources", "youtubeVideos": "YouTube Videos", "activityLog": "Activity Log", "usersList": "Users List", "configuration": "Configuration", "scheduleMeeting": "Schedule Meeting", "meetingList": "Meeting List"}, "exams": {"title": "<PERSON><PERSON>", "addExam": "Add <PERSON>am", "selectCourse": "Select Course", "filterByType": "Filter by Type", "filterByExamName": "Filter by Exam name", "examName": "Exam Name", "validFrom": "<PERSON><PERSON>", "validTo": "Valid upto", "duration": "Duration (minutes)", "noOfQuestions": "No. of Questions", "totalMarks": "Total Marks", "type": "Type", "penalty": "Penalty ?", "view": "View", "edit": "Edit", "delete": "Delete", "startExam": "Start Exam", "publishedStatus": "Published Status", "exportExam": "Export Exam", "updateExamStatus": "Update Exam Status", "linkExamToCourse": "Link <PERSON>am to Course", "draftPrompt": "Do you want to move this exam to Draft ?", "publishPrompt": "Do you want to Publish this exam ?", "searchCourse": "Search course...", "courseName": "Course Name", "selectSection": "Select Section", "noCoursesAvailable": "No courses available", "selectCategroy": "Select Category", "selectFolder": "Select Folder", "selectExamType": "Select Exam Type", "passMark": "Pass Mark", "allowedAttempts": "Allowed Attempts", "penaltyApplicable": "Penalty Applicable ?", "penaltyType": "Penalty Type", "questionBased": "Question based", "examBased": "Exam based", "penaltyMode": "Penalty Mode", "selectPenaltyMode": "Select Penalty Mode", "fixed": "Fixed", "percentageWise": "Percentage Wise", "noOfWrongAnswers": "No. of Wrong Answers", "penalty%": "Penalty (%)", "penaltyMarksDeducted": "Penalty (marks deducted)", "examValidFrom": "<PERSON><PERSON>", "examValidTo": "<PERSON><PERSON>", "premiumContent": "Premium Content", "isEqualWeightage": "Does each question carry equal weightage ?", "marks": "Marks", "selectMarks": "Select Marks", "examRules": "Exam rules/particulars", "selectPenaltyType": "Select Penalty Type", "examDetails": "<PERSON><PERSON>", "fromDate": "From Date: ", "toDate": "To Date: ", "description": "Description", "importQuestions": "Import Questions", "filterByQuestions": "Filter by Questions", "answers": "Answers", "back": "Back", "questions": "Questions", "defaultMark": "De<PERSON><PERSON>", "penalty_": "Penalty", "selectQuestionCategory": "Select Question Category", "import": "Import", "penaltyScore": "Penalty Score", "deletePrompt": "Do you want to delete the Question ?", "question": "Question", "multipleChoice": "Multiple Choice", "examTrialRun": "Exam Trial Run", "start": "Start", "submit": "Submit", "duration:": "Duration:", "timeRemaining": "Time Temaining:", "date": "Date:", "prev": "Prev", "next": "Next", "mark": "Mark:", "penaltyIfApplicable": "Penalty if applicable:", "selectAnAns": "Select an answer:", "examReview": "Exam Review", "quizName": "Quiz Name:", "startDate": "Start Date", "endDate": "End Date", "scoredMark": "Scored Marks", "examDuration": "Duration", "selectedAns": "Selected Answer:", "notAns": "Not Answered", "finish": "Finish"}, "about": {"date": "Date", "description": "This is your display name."}, "auth": {"inputMail": "Input your email", "loginTitle": "Login Success", "loginDescription": "Successfully logged in", "email_and_password": "Enter your email and password to login", "email": "Email", "password": "Password", "rememberMe": "remember Me", "resetPassword": "Reset Password", "newPassword": "New password", "confirmPassword": "Confirm password"}, "errorMessages": {"add_subscription_msg": "Failed to add subscription plan", "add_subscription_title": "Failed to add", "update_subscription_msg": "Failed to update subscription plan", "update_subscription_title": "Failed to update", "update_course_to_plan_title": "Failed to update", "update_resource_to_plan_title": "Failed to update", "delete_subscription_plan_message": "Failed to delete", "approve_subscription_plan_message": "Failed to publish", "delete_course_plan_message": "Failed to delete", "error_fetching_data": "Error fetching data", "toast_error_title": "Error", "toast_variant_destructive": "destructive", "checkpoint_fail": "Failed to add checkpoint details", "check_checkpoint_no": "Please check the number of checkpoints", "check_point_sequence": "Check point sequence must be unique", "check_point_name": "Check point name must be unique", "check_point_time": "Check point start time must be unique", "check_point_resource": "Check point resource must be unique", "check_point_length": "Check point start time exceeds video length", "unique_checkpoint": "Check point name, sequence number, start time and resource id must be unique", "exceeded_checkpoint": "Already exceeded the checkpoint number", "toast_validation_error": "Validation error", "select_role_msg": "Please select a role", "select_user_msg": "Please select a Reporting to", "user_not_found_msg": "User not found in the response", "mandatory_error": "You can only set Manda<PERSON> to true once", "something_went_wrong": "Something went wrong", "user_not_select_msg": "Please select at least one user", "edit_not_allowed": "Edit not allowed for this item", "delete_not_allowed": "Delete not allowed for this item", "publish_not_allowed": "Publish not allowed for this item", "user_to_enroll": "Please select a user to enroll", "course_date": "Course end date should be greater course start date and the difference should be at least 24 hours", "course_not_select_choice": "Please confirm your choice by selecting 'Yes' or 'No'", "comment_status_not_selected": "Please confirm your choice by selecting 'Approve' or 'Reject'", "custom_branding_date_error": "Valid From date should be less than Valid To date", "subscription_add_date": "Invalid validity date:Start date must be before end date, and the difference should be at least 24 hours", "subscription_update_date": "Invalid date range. Start date must be before end date, and the difference should be at least 24 hours", "invalid_date_msg": "Invalid date range", "invalid_validity": "Invalid validity period: Valid from of Subscription plan should not be greater than Val<PERSON> to", "invalid_valid_from": "Valid from of Subscription should not be greater than Valid to.", "select_resource": "Please select only one resource for import", "not_select_resource": "Please select resource", "valid_name": "Please enter a valid name", "valid_description": "Please enter a valid description", "valid_url": "Please enter a valid URL", "enter_checkpoint_no": "Please enter checkpoint number", "send_notification": "Failed to send notification", "send_email": "Failed to send email", "invite_user": "Failed to invite user", "select_valid_image": "Please select a valid image file (PNG or JPG)", "file_size_exceed": "File size exceeds limit.", "select_folder": "Please select a folder before proceeding", "select_one_folder": "Please select at least one resource.", "order_exist": "The following order values already exist", "extend_validity": "Failed to update plan validity", "link_resource_to_course": "Failed to link resource to course", "link_exam_to_course": "Failed to link exam to course", "duplicate_resource_alert": "Duplicate order values detected. Please choose unique order values for each item", "order_mandatory_alert": "Please select an order for all selected resources", "order_already_exist_alert": "The following order values already exist", "custom_branding_msg": "Failed to save custom branding", "publish_meeting_details": "Failed to publish meeting details", "missingUrl": "Please provide either a Meeting ID or Meeting URL", "noDataToExport": "No data available to export!", "chooseDescription": "Please choose organization", "delete_resource_title": "Cannot delete the resource", "description_atleat_one_resource": "You must have at least one resource in your dashboard.", "min_resources_msg": "You must have at least {{count}} resources in your dashboard.", "checkCheckpointNo": "Please check the number of checkpoints", "course_expired": "Course has expired", "pasmark_greaterthan_totalmark": "Pass mark should be less than or equal to total mark.", "mark_for_all_questions": "Marks should be {{mark}} for all questions", "select_category": "Please select a category.", "exam_time_before_course_time": "Ensure that the specified exam start time is not before the course start tim", "exam_time_after_course_time": "Ensure that the specified exam end time is not after the course end time", "excel_format_invalid": "The Excel format is invalid. Please make sure all required columns are present.", "empty_excel": "The uploaded Excel file is empty or has no valid rows.", "no_valid_questions": "No valid questions found in the uploaded file.", "selectQuestionCategory": "Please select a question category.", "select_atleast_one_question": "Please select at least one question to submit.", "questions_added": "Questions added successfully.", "enterAllDetails": "Please enter all details", "enterValidDate": "Please enter a valid title.", "addValidContent": "Please add a valid content.", "bothFilesMandatory": "Both fields are mandatory", "failToAddComments": "Failed to add comments!", "noCourseSelected": "No courses selected. Please select at least one course.", "monthly_date_error": "Please select a date at least one month apart.", "annual_date_error": "Please select a date at least one year apart.", "selectQuestion": "Please select question(s).", "tokenExpired": "Token expired", "date_missing": "Start date or end date is missing.", "wrong_ans_warning": "No. of wrong answers cannot be greater than No. of questions.", "validTitle": "Please enter a valid title.", "exceedFileSize": "File size should not exceed 1 MB.", "selectMaxQuestions": "You can select a maximum of {{count}} questions.", "selectQuestionsToImport": "Please select questions to import", "noQuestionsImported": "No questions were imported because duplicates are not permitted.", "sessionExpired": "Your session has expired. Please log in again.", "selectValidImage": "Please select a valid image file (PNG or JPG)", "cannotRearrange": "<PERSON><PERSON>", "resourceAlreadyAdded": "Resource already added", "sectionRequired": "Section name is required", "addOneSection": "Please add at least one section before submitting.", "failedToImportExam": "Question added to question bank but failed to import to exam.", "selectOneAns": "Please select at least one answer", "questionAlreadyExists": "This question already exists in the question bank."}, "successMessages": {"add_subscription_msg": "Successfully updated subscription plan", "add_subscription_title": "Subscription added", "update_subscription_msg": "Successfully updated subscription plan", "update_subscription_title": "Subscription updated", "update_course_to_plan_msg": "Successfully updated course to plan", "update_course_to_plan_title": "Successfully updated", "update_resource_to_plan_msg": "Successfully updated resource to plan", "update_resource_to_plan_title": "Successfully updated", "subscription_approved_msg": "Subscription approved", "toast_variant_default": "default", "pending_subscription": "Pending subscription", "exam_trial_run": "Exam test Trial completed", "toast_success_title": "Success", "update_user_msg": "User updated successfully", "api_status_success": "success", "course_duplicate_title": "Course Duplicate", "course_duplicate_msg": "Course Duplicated Successfully", "course_add_msg": "Course add succcessfully", "course_add_title": "Course Add", "course_update_msg": "Course Updated Successfully", "course_deleted": "Course deleted", "checkpoint_updated": "Updated checkpoint successfully!", "checkpoint_details": "Added checkpoint details successfully !", "course_delete_msg": "Successfully deleted the Course", "comment_approve": "Comment approved", "comment_reject": "Comment rejected", "comment_approve_msg": "Comment approved successfully", "comment_reject_msg": "Comment rejected successfully", "category_update_title": "Category Updated", "category_add_title": "Category Added", "category": "Category", "category_update_msg": "Category Updated Successfully", "category_add_msg": "Category Added Successfully", "addNewCategory": "Successfully added a new category", "category_cancelled_msg": "Action cancelled", "course_update_title": "Course Updated", "import_resource": "Resource imported successfully", "deleteResourceNotification": "Do you want to delete the resource?", "deleteEventMsg": "Do you want to delete the Event", "deleteResourceMsg": "Resource deleted successfully", "deleteCategoryMsg": "Category deleted successfully", "publishCategoryMsg": "Category published successfully", "draftCategoryMsg": "Category moved to Draft", "publishExamMsg": "Exam published successfully", "userRemovedTitleMsg": "User Removed Successfully", "deleteResourceTitle": "Resource Deleted", "deleteCategoryTitle": "Category Deleted", "publishCategoryTitle": "Category Published", "categoryStatus": "Category Status", "draftCategoryTitle": "Category Status", "pulishExamTitle": "Exam Status", "deaftExamTitle": "Exam Status", "userRemovedTitle": "User Removed", "approvResourceMsg": "Resource approved successfully", "draftResourceMsg": "Resource moved to draft", "approvResourceTitle": "Resource approved", "draftResourceTitle": "Resource Drafted", "approvalNotification": "Do you want to approve the resource?", "draftNotification": "Do you want to draft the resource?", "addPageMsg": "Page content added successfully", "addPageTitle": "Page content added", "editPageMsg": "Page content updated successfully", "editPageTitle": "Page content updated", "toast_variant_success": "success", "resource_add_success": "Added resource successfully!", "resource_update_success": "Resource updated successfully", "add_folder_msg": "Folder added successfully", "add_folder_title": "Folder added", "add_question": "Question added successfully", "edit_question": "Question updated successfully", "quiz_delete_msg": "Question deleted from the quiz successfully", "quiz_delete_title": "Question deleted", "pulishExamMsg": "Exam published", "deaftExamMsg": "<PERSON><PERSON> moved to Draft", "add_user_to_plan_msg": "User Added successfully", "add_user_to_plan_title": "User Added", "send_notification": "Notification sent successfully", "send_email": "<PERSON>ail sent successfully", "invite_user": "User invited successfully", "delete_section": "Section deleted successfully", "delete_section_title": "Section deleted ", "import_folder": "Folder imported successfully", "import_folder_title": "Folder imported", "extend_validity": "Plan validity updated successfully", "link_resource_to_course": "Resource linked to course successfully", "custom_branding_msg": "Custom branding saved successfully", "publish_meeting_details": "Meeting details published successfully", "exam_linked_to_course": "Exam linked to course successfully", "assign_organization": "User assigned to organization successfully", "dashboardUpdated": "Dashboard updated successfully", "addedFile": "Successfully added file to resources !", "enrollment_added": "Enrollments added successfully!", "select_course_and_user": "Please select course and users to enroll", "select_atleast_one_ans": "Please select atleat one answer", "question_already_exists": "This question already exists in the question bank.", "exam_added": "Exam added successfully!", "exam_updated": "Exam updated successfully!", "submit_exam": "Successfully submitted exam", "update_group": "Groups updated", "update_group_success": "Successsfully updated groups", "activate_user": "User Activated", "deactivate_user": "User Deactivated", "activate_user_successfully": "Successfully activated the user", "deactivate_user_successfully": "Successfully deactivated the user", "deactivate_fail": "Failed to deactivate", "user_role_updated": "Successfully updated role", "added_file": "Added file to resources successfully!", "addPageContent": "Added page content successfully!", "commentAdded": "Your comment has been added successfully!", "groupsUpdated": "Groups updated", "successfullyUpdatedGroups": "Successfully updated groups", "deleteTitle": "Deletion Success", "deleteDescription": "Deleted successfully", "eventPublishTitle": "Event Published", "eventPublishDescription": "Successfully published Event", "eventUnpublishTitle": "Event Unublished", "eventUnpublishDescription": "Successfully unpublished Event", "resetTitle": "Reset Mail", "resetDescription": "Reset Password link has been sent to your mail.", "subscriptionDelete": "Subscription Deleted", "subdcriptionDelDesc": "Successfully deleted the subscription", "subscription_status_changed": "Subscription status changed to {{status}}.", "privilegeAdded": "Privileges added successfully!", "deleteQuestionCat": "Question Category Deleted", "deleteQuestionCatDesc": "Successfully deleted question category", "questionCategory": "Question Category", "categoryAdded": "Category Added Successfully", "categoryEdited": "Category Edited Successfully", "questionCatPublished": "Successfully published question category", "questionPublished": "Question Published", "questionPublishDesc": "Successfully published question", "passwordUpdated": "Password udpated successfully", "addGroup": "Successfully added a new group", "groupTitle": "Group Added", "groupDeleted": "Group Deleted", "groupDeleteDesc": "Successfully deleted  group", "currentAffairsAdded": "Current affairs added successfully!", "questions_imported": "{{count}} Questions imported!", "subscriptionUserRemoved": "Subscription User Removed", "subscriptionUserDesc": "Successfully removed the user from subscription list", "categoryUpdated": "Category Updated", "categoryUpdateDesc": "Successfully updated category", "cancelCategory": "Category Cancelled", "cancelCategoryDesc": "You have cancelled the update", "need_at_least_two_sections": "You need at least 2 sections to rearrange. Currently there {{verb}} {{count}} section{{plural}}.", "folderNameUpdate": "Folder name updated successfully", "order_resource": "Resource order updated successfully", "order_folder": "Folder order updated successfully", "order_section": "Section order updated successfully", "questionAddedAndImported": "Question added and imported successfully!"}}