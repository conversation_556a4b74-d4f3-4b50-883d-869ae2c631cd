import type {
  ErrorType,
  LiveClassRequest,
  LiveClassResponse,
  LiveClassUpdateRequest,
  PublishMeetingDetailsReq,
  PublishMeetingResponse,
} from "@/types";
import { supabase } from "../lib/client";
import { rpc } from "@/lib/apiConfig";
interface UseLiveClassReturn {
  publishMeetingDetails: (
    params: PublishMeetingDetailsReq,
  ) => Promise<PublishMeetingResponse>;
  liveClassList: (params: LiveClassRequest) => Promise<LiveClassResponse[]>;
  updateLiveClassDetails: (
    params: LiveClassUpdateRequest,
  ) => Promise<LiveClassResponse[]>;
}

const useLiveClass = (): UseLiveClassReturn => {
  async function publishMeetingDetails(
    formData: PublishMeetingDetailsReq,
  ): Promise<PublishMeetingResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.insertLiveClassDetails,
        formData,
      )) as {
        data: PublishMeetingResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as PublishMeetingResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function liveClassList(
    params: LiveClassRequest,
  ): Promise<LiveClassResponse[]> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.getLiveClassDetails,
        params,
      )) as {
        data: LiveClassResponse[];
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as LiveClassResponse[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
 async function updateLiveClassDetails(
    params: LiveClassUpdateRequest,
  ): Promise<LiveClassResponse[]> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.updateLiveClassDetails,
        params,
      )) as {
        data: LiveClassResponse[];
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as LiveClassResponse[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  return {
    publishMeetingDetails,
    liveClassList,
    updateLiveClassDetails,
  };
};

export default useLiveClass;
