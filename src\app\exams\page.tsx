"use client";
import { getColumns } from "./columns";
import { DataTable } from "../../components/ui/data-table/data-table";
import MainLayout from "../layout/mainlayout";
import { Combobox } from "../../components/ui/combobox";
import { Label } from "@/components/ui/label";
import "../../styles/main.css";
import {
  Edit,
  Eye,
  PlusIcon,
  Play,
  PencilLineIcon,
  Link as ILink,
} from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import type {
  ExamDataType,
  ToastType,
  ErrorCatch,
  EnrollmentTableType,
  QuizesOfCourse,
  InnerItem,
} from "@/types";
import useExams from "../../hooks/useExams";
import React, { useEffect, useState } from "react";
import { useToast } from "@/components/ui/use-toast";
import useCourse from "@/hooks/useCourse";
import { pageUrl, privilegeData } from "@/lib/constants";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { Spinner } from "@/components/ui/progressiveLoader";
import getPrivilegeList from "@/hooks/useCheckPrivilege";
import { Modal } from "@/components/ui/modal";
import PublishExam from "./publishExam";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumb";
import { ExamTypes } from "@/lib/constants";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import ExportExam from "./exportExam";
import { useTranslation } from "react-i18next";
import { ERROR_MESSAGES } from "@/lib/messages";

export default function ExamListPage(): React.JSX.Element {
  const { t } = useTranslation();
  const columns = getColumns(t);
  const { getExamsList } = useExams();
  const { toast } = useToast() as unknown as ToastType;
  const [examData, setExamData] = useState<EnrollmentTableType[]>([]);
  const [filteredExamData, setFilteredExamData] = useState<
    EnrollmentTableType[]
  >([]);
  const [courseData, setCourseData] = useState<
    { value?: string; label?: string }[]
  >([]);
  const { getCourseList } = useCourse();
  const [defaultCourseLabel, setDefaultCourseLabel] = useState<string>("");
  const [selectCourse, setSelectCourse] = useState<string>("");
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const [disableBtn, setDisableBtn] = useState<boolean>(false);
  const searchParams = useSearchParams();
  const variableValue = searchParams.get("variable");
  const [isOpenPulishDialog, setIsOpenPublishDialog] = useState<boolean>(false);
  const [rowExamId, setrowExamId] = useState<string>("");
  const [publishedStatus, setPublishedStatus] = useState<string>("");
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  const [selectType, setSelectType] = useState<string>("");
  const [isOpenExportExam, setIsOpenExportExam] = useState<boolean>(false);
  const [selectedExam, setSelectedExam] = useState<ExamDataType>();

  useEffect(() => {
    setBreadcrumbItems(
      getBreadCrumbItems(t, t("breadcrumb.exams"), { "": "" }),
    );
  }, [t]);

  useEffect(() => {
    setFilteredExamData(examData);
  }, [examData]);

  useEffect(() => {
    if (selectType !== null && selectType !== "All") {
      const filtered = examData.filter(
        (item) =>
          item.quiz_type === selectType && item.course_id === selectCourse,
      );
      setFilteredExamData(filtered);
    } else {
      setFilteredExamData(examData);
    }
  }, [examData, selectType, selectCourse]);

  useEffect(() => {
    const fetchCourseData = async (): Promise<void> => {
      try {
        const courses = await getCourseList("");
        setIsLoading(true);
        if (
          courses !== null &&
          courses !== undefined &&
          Object.keys(courses).length > 0
        ) {
          const filteredCourses = courses
            .filter((course) => course.course_id != null && course.short_name)
            .map((course) => ({
              value: course.course_id,
              defaultCourseLabel,
              label: course.short_name,
            }));
          setCourseData(filteredCourses);
          if (filteredCourses.length > 0) {
            setSelectCourse(filteredCourses[0].value ?? "");
            setDefaultCourseLabel(filteredCourses[0].label ?? "");
          }
        } else {
          setCourseData([]);
        }
        setIsLoading(false);
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err.message,
        });
      }
    };
    fetchCourseData().catch((error) => console.log(error));
    setDisableBtn(getPrivilegeList("Exam", privilegeData.Exam.addExam));
    setSelectType(ExamTypes[0].label as string);
  }, []);

  useEffect(() => {
    examList(true);
  }, [selectCourse, courseData, selectType]);

  const examList = (value: boolean): void => {
    console.log(value);
    const fetchData = async (): Promise<void> => {
      setIsLoading(true);
      try {
        let selectCid = "";
        if (selectCourse !== null) {
          selectCid = selectCourse;
          console.log(selectCid);
        } else if (courseData.length > 0) {
          selectCid = courseData[0].value as string;
        } else {
          console.log("No valid courses available to select");
        }

        if (selectCid !== "") {
          const exams = await getExamsList(selectCid);
          console.log("variableValue", variableValue);

          if (variableValue !== null) {
            const examArray: QuizesOfCourse[] = exams.filter((item) => {
              return item.quiz_type === variableValue;
            });
            setExamData(examArray);
          } else {
            if (exams !== null || exams !== undefined) {
              setExamData(exams);
            } else {
              setExamData([]);
            }
          }
        }
        setIsLoading(false);
      } catch (error: unknown) {
        setIsLoading(false);
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
      }
    };

    fetchData().catch((error) => console.log(error));
  };

  const handleCourseChange = (selectedOption: string): void => {
    setSelectCourse(selectedOption);
    setSelectType(ExamTypes[0].label as string);
  };

  const handleExamType = (selectedOption: string): void => {
    setSelectType(selectedOption);

    if (selectedOption === null) {
      return;
    }
    const filteredData = examData?.filter(
      (item) =>
        item.quiz_type === selectedOption && item.course_id === selectCourse,
    );
    setFilteredExamData(filteredData);
  };

  const router = useRouter();
  examData.map((item) => {
    if (item.publish_status !== "Published") {
      item.hideIcon = false;
    } else {
      item.hideIcon = true;
    }
  });
  examData.map((item) => {
    if (item.publish_status === "Published") {
      item.hideEdit = true;
    } else {
      item.hideEdit = false;
    }
    if (item.num_of_questions !== item.questions_exists) {
      item.hideStart = true;
    } else {
      item.hideStart = false;
    }
  });

  /* const addQuestionView = async (data: ExamDataType): Promise<void> => {
    const examId = data.id;
    try {
      const questionsData = await getQuestions(examId);
      if (questionsData !== null && questionsData !== undefined) {
        const questionCount = questionsData[0].quest_answers.length;
        const questions = questionsData.map((data) => data.num_of_questions);
        const noOfQuestions = questions[0];
        if (noOfQuestions > questionCount) {
          router.push(
            `${pageUrl.addQuestion}?type=${examId}&noOfChoices=${AppConfig.noOfChoices}`,
          );
        } else {
          toast({
            variant: "destructive",
            title: "Error",
            description:
              "Already entered" + " " + `${noOfQuestions}` + " " + "questions",
          });
        }
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: "destructive",
        title: "Error",
        description: err?.message,
      });
    }
  }; */

  const exportExam = (data: ExamDataType): void => {
    setSelectedExam(data);
    setIsOpenExportExam(true);
  };

  const closeExportExam = (): void => {
    setIsOpenExportExam(false);
  };

  const examDetailView = (data: ExamDataType): void => {
    localStorage.setItem("ExamDetails", JSON.stringify(data));
    const examId = data.id;
    // router.push(`${pageUrl.examDetails}?type=${examId}`);
    router.push(
      `${pageUrl.examDetails}?type=${examId}&&courseId=${selectCourse}`,
    );
  };
  const updateExamDetails = (data: ExamDataType): void => {
    // localStorage.setItem("ExamDetails", JSON.stringify(data));
    const examId = data.id;
    router.push(`${pageUrl.editExams}?type=${examId}&&course=${selectCourse}`);
  };

  const examStart = (data: ExamDataType): void => {
    localStorage.setItem("ExamDetails", JSON.stringify(data));
    const examId = data.id;
    router.push(`${pageUrl.startExam}?type=${examId}`);
  };
  const examPublish = (data: ExamDataType): void => {
    setrowExamId(data.id);
    setPublishedStatus(data.publish_status);
    setIsOpenPublishDialog(true);
  };
  const handleCancel = (): void => {
    router.push(pageUrl.dashboard);
  };

  const closePublishDialog = (): void => {
    setIsOpenPublishDialog(false);
  };

  const onReloadAfterLink = (): void => {
    examList(true);
  };

  return (
    <MainLayout>
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <div>
        <span>
          <h1 className="text-2xl font-semibold tracking-tight mb-4">
            {t("exams.title")}
            {disableBtn && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Link href={pageUrl.addExams}>
                      <Button className="bg-ring bg-[#fb8500] hover:bg-[#fb5c00] py-2 md:py-3 px-4 md:px-6 whitespace-nowrap ml-2">
                        <PlusIcon className="h-4 w-4 md:h-5 md:w-5" />
                      </Button>
                    </Link>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{t("exams.addExam")}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </h1>
        </span>
        <div className="border rounded-md p-4 pt-0 bg-[#fff]">
          {/* <div className="grid grid-cols-4 md:grid-cols-2"> */}

          <div className="flex gap-x-4 mt-4">
            <div className="sm:w-1/2 md:w-1/4 gap-x-4">
              <Label>{t("exams.selectCourse")}</Label>
              <Combobox
                data={courseData}
                onSelectChange={handleCourseChange}
                defaultLabel={defaultCourseLabel}
              />
            </div>
            <div className="gap-x-4 sm:w-1/2 md:w-1/4">
              <Label>{t("exams.filterByType")}</Label>
              <Combobox
                data={ExamTypes}
                onSelectChange={handleExamType}
                defaultLabel={selectType}
              />
            </div>
          </div>

          {isLoading ? (
            <Spinner />
          ) : (
            <div>
              <DataTable
                columns={columns}
                data={filteredExamData}
                FilterLabel={t("exams.filterByExamName")}
                FilterBy={"name"}
                disableIcon={"hideIcon"}
                // hideEdit={"hideEdit"}
                hideStart={"hideStart"}
                actions={[
                  {
                    title: t("exams.view"),
                    icon: Eye,
                    color: "#9bbb5c",
                    varient: "icon",
                    isEnable: getPrivilegeList(
                      "Exam",
                      privilegeData.Exam.getExamList,
                    ),
                    handleClick: (val: unknown) =>
                      examDetailView(val as ExamDataType),
                  },
                  {
                    title: t("exams.edit"),
                    icon: Edit,
                    color: "#f29b26",
                    varient: "icon",
                    isEnable: getPrivilegeList(
                      "Exam",
                      privilegeData.Exam.updateExam,
                    ),
                    handleClick: (val: unknown) =>
                      updateExamDetails(val as ExamDataType),
                  },
                  {
                    title: t("exams.startExam"),
                    icon: Play,
                    varient: "icon",
                    color: "#bd392f",
                    isEnable: getPrivilegeList(
                      "Exam",
                      privilegeData.Exam.trialRunExam,
                    ),
                    handleClick: (val: unknown) =>
                      examStart(val as ExamDataType),
                  },
                  {
                    title: t("exams.publishedStatus"),
                    icon: PencilLineIcon,
                    varient: "icon",
                    isEnable: getPrivilegeList(
                      "Exam",
                      privilegeData.Exam.publishExam,
                    ),
                    color: "#445469",
                    handleClick: (val: unknown) =>
                      examPublish(val as ExamDataType),
                  },
                  {
                    title: t("exams.exportExam"),
                    icon: ILink,
                    varient: "icon",
                    color: "#445469",
                    handleClick: (val: unknown) =>
                      exportExam(val as ExamDataType),
                  },
                ]}
                onSelectedDataChange={() => {}}
              />
            </div>
          )}
        </div>
      </div>
      {variableValue != null && (
        <div className="w-full flex justify-end mt-4">
          <div className="px-4">
            <Button
              variant="outline"
              onClick={handleCancel}
              className="w-full sm:w-auto"
            >
              {t("buttons.cancel")}
            </Button>
          </div>
        </div>
      )}
      {isOpenPulishDialog && (
        <Modal
          title={t("exams.updateExamStatus")}
          header=""
          openDialog={isOpenPulishDialog}
          closeDialog={closePublishDialog}
        >
          <PublishExam
            onSave={(value: boolean) => examList(value)}
            onCancel={closePublishDialog}
            examId={rowExamId}
            status={publishedStatus}
          />
        </Modal>
      )}
      {isOpenExportExam && (
        <Modal
          title={t("exams.linkExamToCourse")}
          header=""
          openDialog={isOpenExportExam}
          closeDialog={closeExportExam}
          type="max-w-4xl"
        >
          <ExportExam
            onCancel={closeExportExam}
            examData={selectedExam as ExamDataType}
            onSave={onReloadAfterLink}
          />
        </Modal>
      )}
    </MainLayout>
  );
}
