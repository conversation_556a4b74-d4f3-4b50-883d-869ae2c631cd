import type { CourseProgressedTableType } from "@/types";
import {
  AntennaIcon,
  AtomIcon,
  BookOpenCheckIcon,
  LayoutDashboardIcon,
  NewspaperIcon,
  ShieldQuestionIcon,
  SlidersHorizontalIcon,
  TablePropertiesIcon,
  UsersIcon,
  ComponentIcon,
  Building,
  ShieldCheck,
  Building2,
  UserCheck,
  LibraryIcon,
  // History,
  Shapes,
  Youtube,
  // MessagesSquare,
  PanelLeft,
  VideoIcon,
  MessagesSquare,
  // VideoIcon,
} from "lucide-react";
import type { LucideIcon } from "lucide-react";

export const AppConfig = {
  noOfChoices: 4,
};

export const privilegeData = {
  Activity_List: { getActivityList: "GET_ACTIVITY_LIST" },
  Checkpoint: {
    createCheckpoint: "CREATE_CHECKPOINT",
    createCheckpointSession: "CREATE_CHECKPOINT_SESSION",
    deleteCheckpoint: "DELETE_CHECKPOINT",
    getCheckpoint: "GET_CHECKPOINT",
    startCheckpointQuiz: "START_CHECKPOINT_QUIZ",
  },

  Course: {
    addCourse: "ADD_COURSE",
    getCourse: "GET_COURSE",
    copyCourse: "COPY_COURSE",
    updateCourse: "UPDATE_COURSE",
    deleteCourse: "DELETE_COURSE",
    getCourseList: "GET_COURSE_LIST",
    courseRankList: "COURSE_RANK_LIST",
    reviewCourseContent: "REVIEW_COURSE_CONTENT",
    updateCoursePublish: "UPDATE_COURSE_PUBLISH",
  },

  Course_Resource: {
    addComment: "ADD_COMMENT",
    addResource: "ADD_RESOURCE",
    deleteResource: "DELETE_RESOURCE",
    getComment: "GET_COMMENT",
    getCourseProgress: "GET_COURSE_PROGRESS",
    getResource: "GET_RESOURCE",
    getResourceList: "GET_RESOURCE_LIST",
    updateCourseApprove: "UPDATE_COURSE_APPROVE",
    updateCourseProgress: "UPDATE_COURSE_PROGRESS",
    updateCourseReview: "UPDATE_COURSE_REVIEW",
    updateResource: "UPDATE_RESOURCE",
  },

  Current_Affairs: {
    addCurrentAffairs: "ADD_CURRENT_AFFAIRS",
    deleteCurrentAffairs: "DELETE_CURRENT_AFFAIRS",
    getCurrentAffairList: "GET_CURRENT_AFFAIR_lIST",
    publishCurrentAffairs: "GET_CURRENT_AFFAIR_lIST",
  },

  Dashboard: {
    dashBoardSettings: "DASHBOARD_SETTINGS",
    getDashboard: "GET_DASHBOARD",
    customDashboard: "CUSTOM_DASHBOARD",
  },

  Enrollments: {
    addEnrollment: "ADD_ENROLLMENT",
    getEnrollmentList: "GET_ENROLLMENT_LIST",
    removeStudent: "REMOVE_STUDENT",
    updateEnrollment: "UPDATE_ENROLLMENT",
  },

  Evaluation: {
    reviewExam: "REVIEW_EXAM",
    evaluateExam: "EVALUATE_EXAM",
    publishResult: "PUBLISH_RESULT",
    getAttendedExamList: "GET_ATTENDED_EXAM_LIST",
  },

  Exam: {
    addExam: "ADD_EXAM",
    addQuestionToExam: "ADD_QUESTION_TO_EXAM",
    fetchResult: "FETCH_RESULT",
    fetchReviewList: "FETCH_REVIEW_LIST",
    getExamList: "GET_EXAM_LIST",
    getQuizQuestions: "GET_QUIZ_QUESTIONS",
    publishExam: "PUBLISH_EXAM",
    startQuiz: "START_QUIZ",
    trialRunExam: "TRIAL_RUN_EXAM",
    updateExam: "UPDATE_EXAM",
    deleteExam: "DELETE_EXAM",
  },

  Group: {
    addGroupCourse: "ADD_GROUP_COURSE",
    addGroupUser: "ADD_GROUP_USER",
    createGroup: "CREATE_GROUP",
    deleteGroup: "DELETE_GROUP",
    listGroup: "LIST_GROUP",
    listGroupCourse: "LIST_GROUP_COURSE",
    listGroupPrivilege: "LIST_GROUP_PRIVILEGE",
    listGroupUser: "LIST_GROUP_USER",
    updateGroup: "UPDATE_GROUP",
    updateGroupCourse: "UPDATE_GROUP_COURSE",
    updateGroupPrivilege: "UPDATE_GROUP_PRIVILEGE",
    updateGroupUser: "UPDATE_GROUP_USER",
  },

  Login: {
    deviceInfo: "DEVICE_INFO",
    pushNotification: "PUSH_NOTIFICATION",
  },

  Notification: {
    addNotification: "ADD_NOTIFICATION",
    deleteNotification: "DELETE_NOTIFICATION",
    getNotificationList: "GET_NOTIFICATION_LIST",
    publishNotification: "PUBLISH_NOTIFICATION",
  },

  Organization: {
    listOrganization: "LIST_ORGANISATION",
  },

  Question_Bank: {
    addQuestion: "ADD_QUESTION",
    addQuestionBank: "ADD_QUESTION_BANK",
    addQuestionCategory: "ADD_QUESTION_CATEGORY",
    approveQuestion: "APPROVE_QUESTION",
    deleteQuestion: "DELETE_QUESTION",
    exportQuestion: "EXPORT_QUESTION",
    getQuestionBankList: "GET_QUESTION_BANK_LIST",
    importQuestion: "IMPORT_QUESTION",
    updateQuestion: "UPDATE_QUESTION",
    publishQuestionCategory: "APPROVE_QUESTION_CATEGORY",
  },

  Role: {
    assignPrivilege: "ASSIGN_PRIVILEGE",
    assignRole: "ASSIGN_ROLE",
    getOrgRole: "GET_ORG_ROLE",
    getPrivilege: "GET_PRIVILEGE",
    getUserPrivilege: "GET_USER_PRIVILEGE",
    updateRolePrivilege: "UPDATE_ROLE_PRIVILEGE",
  },

  Settings: {
    addSettings: "ADD_SETTINGS",
    getSettingsList: "GET_SETTINGS_LIST",
  },

  Subscription_Plans: {
    addNewSubscription: "ADD_NEW_SUBSCRIPTION",
    getSubscriptionList: "GET_SUBSCRIPTION_LIST",
    updateCourseToSubscription: "UPDATE_COURSE_TO_SUBSCRIPTION",
    updateSubscription: "UPDATE_SUBSCRIPTION",
    userSubscriptionList: "USER_SUBSCRIPTION_LIST",
    userSubscriptionPendingList: "USER_SUBSCRIPTION_PENDING_LIST",
    getPendingUserListForPlan: "get_pending_user_list_for_plan",
    addPendingUserListToPlan: "add_pending_user_list_to_plan",
    deleteSubcriptionPlan: "DELETE_SUBSCRIPTION",
    publishSubscriptionPlan: "PUBLISHSUBSCRIPTION",
    deleteUserFromPlan: "REMOVE_USER_FROM_SUBSCRIPTION_PLAN",
    publishSubscription: "APPROVE_SUBSCRIPTION_PLAN",
  },

  Topic: {
    addMainCategory: "ADD_MAIN_CATEGORY",
    addParentCourseCategory: "ADD_PARENT_COURSE_CATEGORY",
    addQuestionCategory: "ADD_QUESTION_CATEGORY",
    addSubCategory: "ADD_SUB_CATEGORY",
    deleteCategory: "DELETE_CATEGORY",
    getCategoryList: "GET_CATEGORY_LIST",
    updateCategory: "UPDATE_CATEGORY",
    PublishCategory: "APPROVE_CATEGORY",
  },

  User: {
    addUser: "ADD_USER",
    getUserList: "GET_USER_LIST",
    updateUserProfile: "UPDATE_USER_PROFILE",
  },

  Resource_Bank: {
    addResource: "RB_ADD_RESOURCE",
    deleteResource: "RB_DELETE_RESOURCE",
    exportResource: "RB_EXPORT_RESOURCE",
    updateResource: "RB_UPDATE_RESOURCE",
    approveResource: "RB_APPROVE_RESOURCE",
    getResourceList: "RB_GET_RESOURCE_LIST",
    addCourseResource: "RB_ADD_COURSE_RESOURCE",
  },

  Question_Category: {
    addQuestionCategory: "ADD_QUESTION_CATEGORY",
    deleteQuestionCategory: "DELETE_QUESTION_CATEGORY",
    updateQuestionCategory: "UPDATE_QUESTION_CATEGORY",
  },
  Live_Class: {
    addLiveClass: "RB_MANAGE_LIVE_CLASS",
  },
};

export const getSideMenu = (
  t: (key: string) => string,
): {
  name: string;
  href: string;
  icon: LucideIcon;
  color: string;
  screen: string;
  action: string;
  canPerformAction: boolean;
}[] => [
  {
    name: t("sideMenu.dashboard"),
    href: "/dashboard",
    icon: LayoutDashboardIcon,
    color: "white",
    screen: "Dashboard",
    action: privilegeData.Dashboard.getDashboard,
    canPerformAction: false,
  },
  {
    name: t("sideMenu.organization"),
    href: "/organization",
    icon: Building2,
    color: "white",
    screen: "Organization",
    action: privilegeData.Organization.listOrganization,
    canPerformAction: false,
  },
  {
    name: t("sideMenu.customDashboard"),
    href: "/custom-dashboard",
    icon: PanelLeft,
    color: "white",
    screen: "Dashboard",
    action: privilegeData.Dashboard.customDashboard,
    canPerformAction: false,
  },
  {
    name: t("sideMenu.category"),
    href: "/topics",
    icon: TablePropertiesIcon,
    color: "white",
    screen: "Topic",
    action: privilegeData.Topic.getCategoryList,
    canPerformAction: false,
  },
  {
    name: t("sideMenu.courses"),
    href: "/courses",
    icon: AtomIcon,
    color: "white",
    screen: "Course",
    action: privilegeData.Course.getCourseList,
    canPerformAction: false,
  },
  {
    name: t("sideMenu.enrollments"),
    href: "/enrollments",
    icon: SlidersHorizontalIcon,
    color: "white",
    screen: "Enrollments",
    action: privilegeData.Enrollments.getEnrollmentList,
    canPerformAction: false,
  },
  {
    name: t("sideMenu.exams"),
    href: "/exams",
    icon: ShieldQuestionIcon,
    color: "white",
    screen: "Exam",
    action: privilegeData.Exam.getExamList,
    canPerformAction: false,
  },
  {
    name: t("sideMenu.questionCategory"),
    href: "/question-category",
    icon: Shapes,
    color: "white",
    screen: "Question_Category",
    action: privilegeData.Question_Category.addQuestionCategory,
    canPerformAction: false,
  },
  {
    name: t("sideMenu.questionBank"),
    href: "/questionbank",
    icon: AntennaIcon,
    color: "white",
    screen: "Question_Bank",
    action: privilegeData.Question_Bank.getQuestionBankList,
    canPerformAction: false,
  },
  {
    name: t("sideMenu.attendedExams"),
    href: "/attended-exams",
    icon: BookOpenCheckIcon,
    color: "white",
    screen: "Evaluation",
    action: privilegeData.Evaluation.getAttendedExamList,
    canPerformAction: false,
  },
  {
    name: t("sideMenu.users"),
    href: "/users-list",
    icon: UsersIcon,
    color: "white",
    screen: "User",
    action: privilegeData.User.getUserList,
    canPerformAction: false,
  },
  {
    name: t("sideMenu.groups"),
    href: "/teams",
    icon: ComponentIcon,
    color: "white",
    screen: "Group",
    action: privilegeData.Group.listGroup,
    canPerformAction: false,
  },

  {
    name: t("sideMenu.currentAffairs"),
    href: "/current-affairs",
    icon: NewspaperIcon,
    color: "white",
    screen: "Current_Affairs",
    action: privilegeData.Current_Affairs.getCurrentAffairList,
    canPerformAction: false,
  },
  {
    name: t("sideMenu.assignOrganization"),
    href: "/assign-organization",
    icon: Building,
    color: "red",
    screen: "Role",
    action: privilegeData.Role.assignRole,
    canPerformAction: false,
  },
  {
    name: t("sideMenu.accessPrivileges"),
    href: "/privileges",
    icon: ShieldCheck,
    color: "white",
    screen: "Role",
    action: privilegeData.Role.getUserPrivilege,
    canPerformAction: false,
  },
  {
    name: t("sideMenu.subscriptionPlans"),
    href: "/membership-plan-list",
    icon: UserCheck,
    color: "white",
    screen: "Subscription_Plans",
    action: privilegeData.Subscription_Plans.getSubscriptionList,
    canPerformAction: false,
  },
  {
    name: t("sideMenu.resourceLibrary"),
    href: "/resources-library",
    icon: LibraryIcon,
    color: "white",
    screen: "Resource_Bank",
    action: privilegeData.Resource_Bank.getResourceList,
    canPerformAction: false,
  },
  {
    name: t("sideMenu.youtubeVideos"),
    href: "/youtube-videos",
    icon: Youtube,
    color: "white",
    screen: "Course_Resource",
    action: privilegeData.Course_Resource.getResourceList,
    canPerformAction: false,
  },
  // {
  //   name: "Activity Log",
  //   href: "/activity-logs",
  //   icon: History,
  //   color: "white",
  //   screen: "Activity_List",
  //   action: privilegeData.Activity_List.getActivityList,
  //   canPerformAction: false,
  // },
  {
    name: t("sideMenu.commentsFeedback"),
    href: "/comments",
    icon: MessagesSquare,
    color: "white",
    screen: "Course_Resource",
    action: privilegeData.Course_Resource.getComment,
    canPerformAction: false,
  },

  {
    name: t("sideMenu.ScheduleClass"),
    href: "/meeting-list",
    icon: VideoIcon,
    color: "white",
    screen: "Live_Class",
    action: privilegeData.Live_Class.addLiveClass, // need to add privilege for live class
    canPerformAction: false,
  },
];

export const pageUrl = {
  addCourse: "/add-course",
  editCourse: "/edit-course",
  courseDetails: "/course-details",
  addExams: "/add-exams",
  editExams: "/edit-exams",
  addPageContent: "/add-page-content",
  resourceListView: "/course-resource",
  examDetails: "/exam-details",
  addQuestion: "/add-question",
  courseList: "/courses",
  currentAffairs: "/current-affairs",
  addCurrentAffairs: "add-current-affairs",
  enrollments: "/enrollments",
  addEnrollments: "/add-enrollments",
  addResources: "/add-resources",
  addUser: "/add-users",
  usersList: "/users-list",
  exams: "/exams",
  teams: "/teams",
  addUserToTeams: "/user-group",
  addCourseToTeams: "/course-group",
  questionBank: "/questionbank",
  addGroupPrivileges: "/group-privileges",
  startExam: "/exam-trial",
  sessionDetails: "/session-details",
  dashboard: "/dashboard",
  addMembershipPlan: "/membership-plan",
  membershipPlanList: "/membership-plan-list",
  courseResourceLink: "/course-resource-link",
  ResourceLibraryLink: "/resources-library",
  topics: "/topics",
  CourseManagement: "/view-course",
  scheduleMeeting: "/scheduleMeeting",
  meetingList: "/meeting-list",
  // usersLink: "/users-link",
};

export const resourceTypes = [
  {
    name: "File",
    value: 1,
    reference: "file",
  },
  {
    name: "Page",
    value: 2,
    reference: "page",
  },
  {
    name: "Quiz",
    value: 3,
    reference: "quiz",
  },
  {
    name: "Video",
    value: 4,
    reference: "video",
  },
];
export const uploadTypes = [
  {
    label: "URL",
    value: "1",
    isDisabled: false,
  },
  /* {
    label: "Upload",
    value: "2",
    isDisabled: false,
  }, */
];
export const milestoneType = [
  {
    name: "Alert",
    value: "alert",
    reference: "alert",
  },
  {
    name: "Yes/No Prompt",
    value: "yes/no prompt",
    reference: "yes/no prompt",
  },
  {
    name: "Input",
    value: "input",
    reference: "input",
  },
  {
    name: "Exam",
    value: "exam",
    reference: "exam",
  },
];

export const workerurl = {
  url: "https://unpkg.com/pdfjs-dist@3.10.111/build/pdf.worker.min.js",
};

export const questionType = [
  {
    value: "Multiple Choice",
    label: "Multiple Choice",
  },
  {
    value: "True/False",
    label: "True/False",
  },
  {
    value: "Matching",
    label: "Matching",
  },
  {
    value: "Short Answer",
    label: "Short Answer",
  },
  {
    value: "Numerical",
    label: "Numerical",
  },
  {
    value: "Essay",
    label: "Essay",
  },
];

export const contentType = [
  {
    value: "PLAIN_TEXT",
    label: "TEXT",
  },
  {
    value: "HTML",
    label: "HTML",
  },
];

export const months = [
  {
    value: "January",
    label: "January",
  },
  {
    value: "February",
    label: "February",
  },
  {
    value: "March",
    label: "March",
  },
  {
    value: "April",
    label: "April",
  },
  {
    value: "May",
    label: "May",
  },
  {
    value: "June",
    label: "June",
  },
  {
    value: "July",
    label: "July",
  },
  {
    value: "August",
    label: "August",
  },
  {
    value: "September",
    label: "September",
  },
  {
    value: "October",
    label: "October",
  },
  {
    value: "November",
    label: "November",
  },
  {
    value: "December",
    label: "December",
  },
];

export const planFrequency = [
  {
    name: "One time",
    value: "one time",
  },
  {
    name: "Monthly",
    value: "monthly",
  },
  {
    name: "Yearly",
    value: "yearly",
  },
];

export const examTypes = [
  {
    name: "Practice",
    value: "Practice",
  },
  {
    name: "Main",
    value: "Main",
  },
  {
    name: "Checkpoint",
    value: "checkpoint",
  },
];
export const marksTypes = [
  {
    name: "1 Mark",
    value: "1",
  },
  {
    name: "2 Marks",
    value: "2",
  },
  {
    name: "3 Marks",
    value: "3",
  },
  {
    name: "4 Marks",
    value: "4",
  },
  {
    name: "5 Marks",
    value: "5",
  },
  {
    name: "10 Marks",
    value: "10",
  },
  {
    name: "15 Marks",
    value: "15",
  },
];

export const courseTypes = [
  {
    name: "Free",
    value: "Free",
  },
  {
    name: "Demo",
    value: "Demo",
  },
  {
    name: "Paid",
    value: "Paid",
  },
];

export const planBasedOn = [
  {
    name: "Course",
    value: "course",
  },
  {
    name: "Resource",
    value: "resource",
  },
];

export const categoryType = [
  {
    value: "Parent Category",
    label: "Parent Category",
  },
  {
    value: "Main Category",
    label: "Main Category",
  },
  {
    value: "Sub Category",
    label: "Sub Category",
  },
];

export const UserFilter = [
  {
    value: "All",
    label: "All",
  },
  {
    value: "Active",
    label: "Active",
  },
  {
    value: "Inactive",
    label: "Inactive",
  },
];

export const StatusTypes = [
  {
    value: "Published",
    label: "Published",
  },
  {
    value: "Draft",
    label: "Draft",
  },
];

export const ExamTypes = [
  {
    value: "All",
    label: "All",
  },
  {
    value: "practice",
    label: "Practice",
  },
  {
    value: "main",
    label: "Main",
  },
  {
    value: "checkpoint",
    label: "Checkpoint",
  },
];

export const ResourceExtensions = {
  image: [
    { value: "png", label: "png" },
    { value: "jpeg", label: "jpeg" },
    { value: "jpg", label: "jpg" },
    { value: "pdf", label: "pdf" },
    { value: "ppt", label: "ppt" },
    { value: "doc", label: "doc" },
    { value: "docx", label: "docx" },
    { value: "xls", label: "xls" },
    { value: "xlsx", label: "xlsx" },
  ],
  video: [
    { value: "mp4", label: "mp4" },
    { value: "wmv", label: "wmv" },
    { value: "avi", label: "avi" },
    { value: "mov", label: "mov" },
  ],
  page: [{ value: "html", label: "html" }],
};

export const CommentsData = [
  {
    slno: 1,
    comment: "Great work on the project!",
    subject: "Project Review",
    comment_type: "Feedback",
    user_name: "JohnDoe",
    received_date: "2024-09-05",
  },
  {
    slno: 2,
    comment: "Please review the last section again.",
    subject: "Project Review",
    comment_type: "Feedback",
    user_name: "JaneSmith",
    received_date: "2024-09-04",
  },
  {
    slno: 3,
    comment: "The UI looks amazing!",
    subject: "Project Review",
    comment_type: "Suggestion",
    user_name: "MikeJohnson",
    received_date: "2024-09-03",
  },
  {
    slno: 4,
    comment: "Can we add more details to the report?",
    subject: "Project Review",
    comment_type: "Feedback",
    user_name: "EmilyClark",
    received_date: "2024-09-02",
  },
  {
    slno: 5,
    comment: "I found a bug in the latest update.",
    subject: "Project Review",
    comment_type: "Suggestion",
    user_name: "DavidBrown",
    received_date: "2024-09-01",
  },
];

export const EXAM_TYPE_MAIN = "Main";
export const EXAM_TYPE_PRACTICE = "Practice";
export const EXAM_TYPE_CHECKPOINT = "checkpoint";
export const MODULE_TYPE_ID = "dd33330a-7ab4-452b-b8bd-9e37192f0ffb";
export const ORG_KEY = "orgId";
export const ORG_NAME = "orgName";
export const ACCESS_TOKEN = "access_token";
export const API_RESPONSE_SUCCESS = "success";
export const API_RESPONSE_ERROR = "error";

export const SESSION_LABEL_ENROLLED = "List of users enrolled";
export const SESSION_LABEL_WATCHED = "List of users watched";
export const SESSION_LABEL_NOT_WATCHED = "List of users not watched";
export const SESSION_LABEL_PASSED = "List of users passed";
export const SESSION_LABEL_FAILED = "List of users failed";
export const SESSION_LABEL_PENDING = "List of users with in-progress status";

export const SESSION_GRAPH_ENROLLED_INDEX = 0;
export const SESSION_GRAPH_WATCHED_INDEX = 1;
export const SESSION_GRAPH_NOT_WATCHED_INDEX = 2;
export const SESSION_GRAPH_PASSED_INDEX = 3;
export const SESSION_GRAPH_FAILED_INDEX = 4;
export const SESSION_GRAPH_PENDING_INDEX = 5;

export const SESSION_FILTER_TYPE_ENROLLED = "enrolled";
export const SESSION_FILTER_TYPE_WATCHED = "watched";
export const SESSION_FILTER_TYPE_NOT_WATCHED = "not_watched";
export const SESSION_FILTER_TYPE_PASSED = "passed";
export const SESSION_FILTER_TYPE_FAILED = "failed";
export const SESSION_FILTER_TYPE_PENDING = "in progress";

export const DEFAULT_COURSE_ID = "5c246a6b-e48d-4bc7-80ab-61fa73572fe0";
export const DEFAULT_COURSE_NAME = "Natural science -Batch 1";
export const DEFAULT_MODULE_ID = "38782594-a8db-429d-a6db-50981d79a161";
export const DEFAULT_MODULE_NAME = "Artic Animals - For Kids";
export const DEFAULT_USER_ID = "04386a85-6c49-4194-96cc-7905c1b4f114";
export const DEFAULT_USER_NAME = "Rahul KR";
export const DOCUMENT_DRAFT = "Draft";
export const DOCUMENT_PUBLISHED = "Published";

export const ADD_EXAM_SCHEMA_ERROR_MSG_QUIZ_TYPE = "Please select an exam type";
export const ADD_EXAM_SCHEMA_ERROR_MSG_NAME = "Please enter exam name";
export const ADD_EXAM_SCHEMA_ERROR_MSG_NUM_OF_QUESTIONS =
  "Please enter number of questions";
export const ADD_EXAM_SCHEMA_ERROR_MSG_TOTAL_MARK = "Please enter total mark";
export const ADD_EXAM_SCHEMA_ERROR_MSG_NUMBER = "Please enter a valid number";
export const ADD_EXAM_SCHEMA_ERROR_MSG_PASS_MARK = "Please enter pass mark";
export const ADD_EXAM_SCHEMA_ERROR_MSG_DURATION =
  "Please enter duration in minutes";
export const ADD_EXAM_SCHEMA_ERROR_MSG_ALLOWED_ATTEMPTS =
  "Please enter allowed attempts";
export const ADD_EXAM_SCHEMA_ERROR_MSG_START_TIME =
  "Please enter course start date";
export const ADD_EXAM_SCHEMA_MSG_ONE_START_TIME =
  "Start date must be greater than or equal the current date";
export const ADD_EXAM_SCHEMA_MSG_TWO_START_TIME =
  "Please provide a valid time. You have enterd a past time";
export const ADD_EXAM_SCHEMA_ERROR_MSG_END_TIME =
  "Please enter course end date";
export const ADD_EXAM_SCHEMA_MSG_ONE_END_TIME =
  "End date must be greater than the current date";
export const ADD_EXAM_SCHEMA_MSG_TWO_END_TIME =
  "Please provide a valid time. You have enterd a past time";
export const ADD_EXAM_SCHEMA_ERROR_MSG_DESCRIPTION = "Please enter the content";
export const ADD_EXAM_SCHEMA_DATE_FORMAT = "DD/MM/YYYY";
export const ADD_EXAM_SCHEMA_TIME_FORMAT = "HH:mm";
export const ADD_CATEGORY_IN_COURSE_MSG_NAME = "Please enter category name";
export const ADD_CATEGORY_IN_COURSE_MSG_CATEGORY_TYPE =
  "Please select category type";
export const ADD_CATEGORY_IN_COURSE_MSG_TOPIC = "Please select topic";
export const ADD_CATEGORY_IN_COURSE_MSG_DESCRIPTION =
  "Please enter description";
export const TIME_ZONE = "Asia/Calcutta";
export const FORMATTED_DATE_FORMAT = "MM-DD-YYYY, hh:mm A";
export const DATE_FORMAT = "YYYY-MM-DDTHH:mm";
export const DATE_FORMAT_WITH_SECONDS = "YYYY-MM-DD HH:mm:ss";
export const DATE_FORMAT_WITHOUT_SECONDS = "YYYY-MM-DD HH:mm";
export const DATE_FORMAT_DMY_HM_AM_PM = "DD-MMM-YYYY hh:mm a";
export const DAY = "day";
export const DAYS = "days";
export const MINUTE = "minute";
export const ERROR_FETCHING_DATA = "Error fetching data";
export const PDF = "pdf";
export const JPG = "jpg";
export const JPEG = "jpeg";
export const GIF = "gif";
export const BMP = "bmp";
export const PNG = "png";
export const ANUAL_PLAN = "Annually";
export const MONTHLY_PLAN = "Monthly";
export const CUSTOM_PLAN = "Custom";
export const COURSE_BASED = "Course Based";
export const RESOURCE_BASED = "Resource Based";
export const TIME_BASED = "Time Based";
export const DEFAULT_CURRENCY = "INR";
export const LOGOUT_CONFIRMATION = "Would you like to log out of the session?";
export const File = "File";
export const Video = "Video";
export const YoutubeUrl = "https://youtu.be/";
export const CONFIG_VALUE = "config_value";
export const DEVELOPMENT = "DEVELOPMENT";
export const CHECKPOINT = "Checkpoint";
export const COURSE_DRAFT = "Draft";
export const COURSE_PUBLISHED = "Published";

export const COUNTRY_CODES = [
  { code: "+91", name: "India" },
  { code: "+1", name: "United States/Canada" },
  { code: "+44", name: "United Kingdom" },
  { code: "+61", name: "Australia" },
  { code: "+81", name: "Japan" },
  { code: "+49", name: "Germany" },
  { code: "+86", name: "China" },
  { code: "+33", name: "France" },
  { code: "+55", name: "Brazil" },
  { code: "+7", name: "Russia" },
  // Add more country codes as needed
];
export const COURSE_PROGRESSED_USERS: CourseProgressedTableType[] = [
  {
    result: "success",
    user_id: "123",
    first_name: "John",
    last_name: "Mahib",
    course_id: "2222",
    course_name: "Security Training",
    instance_id: "3333",
    instance_name: "Security PPT",
    instance_progress: "3",
    progress_attened_on: "03-Oct-2024",
    email: "<EMAIL>",
  },
  {
    result: "success",
    user_id: "123",
    first_name: "Rathul",
    last_name: "Ram",
    course_id: "2222",
    course_name: "Security Training",
    instance_id: "3333",
    instance_name: "Security Video",
    instance_progress: "10.00",
    progress_attened_on: "08-Oct-2024",
    email: "<EMAIL>",
  },
  {
    result: "success",
    user_id: "123",
    first_name: "Tony",
    last_name: "Abraham",
    course_id: "2222",
    course_name: "Security Training",
    instance_id: "3333",
    instance_name: "Security PPT",
    instance_progress: "4",
    progress_attened_on: "08-Oct-2024",
    email: "<EMAIL>",
  },
];
export const extractVideoIdFromSearch = (url: string): string | null => {
  try {
    // Check if the URL contains a "vid:" pattern inside fragments
    const vidMatch = url.match(/vid:([a-zA-Z0-9_-]{11})/);
    return vidMatch ? vidMatch[1] : null;
  } catch {
    return null;
  }
};
export const DEFAULT_FOLDER_ID = "00000000-0000-0000-0000-000000000000";

export const logos = [
  "Main Logo",
  "Mobile Logo",
  // "Webicon",
  "Banner Image",
];
export const MEETING_PLATFORM = [
  { value: "zoom", label: "Zoom" },
  {
    value: "gmeet",
    label: "Google Meet",
  },
];

export const YOUTUBE_KEY = "AIzaSyC53ajCxV4LoX01gUo7WtgoMp-J6fqAipg";
export const formatDate = (isoString: string): string => {
  const date = new Date(isoString);
  const dd = String(date.getDate()).padStart(2, "0");
  const mm = String(date.getMonth() + 1).padStart(2, "0");
  const yyyy = date.getFullYear();

  let hh = date.getHours();
  const ampm = hh >= 12 ? "PM" : "AM";
  hh = hh % 12;
  hh = hh === 0 ? 12 : hh; // convert 0 to 12

  const hhStr = String(hh).padStart(2, "0");
  const mi = String(date.getMinutes()).padStart(2, "0");
  const ss = String(date.getSeconds()).padStart(2, "0");

  return `${dd} ${mm} ${yyyy} ${hhStr}:${mi}:${ss} ${ampm}`;
};
