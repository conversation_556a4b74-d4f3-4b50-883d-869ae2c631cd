"use client";
import type { InnerItem } from "@/types";

type QueryParams = Record<string, string | boolean | number>;

const getBreadCrumbItems = (
  t: (key: string) => string,
  screenName: string,
  query: QueryParams = {},
): InnerItem[] => {
  const commonItems = [
    { name: t("breadcrumb.dashboard"), path: "/dashboard", isQuery: false },
  ];
  const breadcrumbItems = [
    {
      screen_name: t("breadcrumb.editCourse"),
      child_screens: [
        { name: t("breadcrumb.courses"), path: "/courses", isQuery: false },
        {
          name: t("breadcrumb.viewCourse"),
          path: "/view-course",
          isQuery: true,
        },
        {
          name: t("breadcrumb.updateCourse"),
          path: "/edit-course",
          isQuery: true,
        },
      ],
    },
    {
      screen_name: t("breadcrumb.organization"),
      child_screens: [
        {
          name: t("breadcrumb.organization"),
          path: "/organization",
          isQuery: false,
        },
      ],
    },
    {
      screen_name: t("breadcrumb.topics"),
      child_screens: [
        { name: t("breadcrumb.category"), path: "/topics", isQuery: false },
      ],
    },
    {
      screen_name: t("breadcrumb.course"),
      child_screens: [
        { name: t("breadcrumb.course"), path: "/courses", isQuery: false },
      ],
    },
    {
      screen_name: t("breadcrumb.viewCourse"),
      child_screens: [
        { name: t("breadcrumb.course"), path: "/courses", isQuery: false },
        {
          name: t("breadcrumb.viewCourse"),
          path: "/view-course",
          isQuery: true,
        },
      ],
    },
    {
      screen_name: t("breadcrumb.enrollments"),
      child_screens: [
        {
          name: t("breadcrumb.enrollments"),
          path: "/enrollments",
          isQuery: false,
        },
      ],
    },
    {
      screen_name: t("breadcrumb.addEnrollments"),
      child_screens: [
        {
          name: t("breadcrumb.enrollments"),
          path: "/enrollments",
          isQuery: false,
        },
        {
          name: t("breadcrumb.addEnrollments"),
          path: "/add-enrollments",
          isQuery: false,
        },
      ],
    },
    {
      screen_name: t("breadcrumb.exams"),
      child_screens: [
        { name: t("breadcrumb.exams"), path: "/exams", isQuery: false },
      ],
    },
    {
      screen_name: t("breadcrumb.addExams"),
      child_screens: [
        { name: t("breadcrumb.exams"), path: "/exams", isQuery: false },
        { name: t("breadcrumb.addExams"), path: "/add-exams", isQuery: false },
      ],
    },
    {
      screen_name: t("breadcrumb.addExams"),
      child_screens: [
        { name: t("breadcrumb.exams"), path: "/exams", isQuery: false },
        { name: t("breadcrumb.addExams"), path: "/add-exams", isQuery: false },
      ],
    },
    {
      screen_name: t("breadcrumb.questionCategory"),
      child_screens: [
        {
          name: t("breadcrumb.questionCategory"),
          path: "/question-category",
          isQuery: false,
        },
      ],
    },
    {
      screen_name: t("breadcrumb.questionBank"),
      child_screens: [
        {
          name: t("breadcrumb.questionBank"),
          path: "/questionbank",
          isQuery: false,
        },
      ],
    },
    {
      screen_name: t("breadcrumb.addQuestion"),
      child_screens: [
        {
          name: t("breadcrumb.questionBank"),
          path: "/questionbank",
          isQuery: false,
        },
        {
          name: t("breadcrumb.addQuestion"),
          path: "/add-question",
          isQuery: false,
        },
      ],
    },
    {
      screen_name: t("breadcrumb.attendedExams"),
      child_screens: [
        {
          name: t("breadcrumb.attendedExams"),
          path: "/attended-exams",
          isQuery: false,
        },
      ],
    },
    {
      screen_name: t("breadcrumb.users"),
      child_screens: [
        { name: t("breadcrumb.users"), path: "/users-list", isQuery: false },
      ],
    },
    {
      screen_name: t("breadcrumb.examDetails"),
      child_screens: [
        { name: t("breadcrumb.exams"), path: "/exams", isQuery: false },
        {
          name: t("breadcrumb.examDetails"),
          path: "/exam-details",
          isQuery: false,
        },
      ],
    },
    {
      screen_name: t("breadcrumb.groups"),
      child_screens: [
        { name: t("breadcrumb.groups"), path: "/teams", isQuery: false },
      ],
    },
    {
      screen_name: t("breadcrumb.userGroup"),
      child_screens: [
        { name: t("breadcrumb.groups"), path: "/teams", isQuery: false },
        {
          name: t("breadcrumb.userGroup"),
          path: "/user-group",
          isQuery: false,
        },
      ],
    },
    {
      screen_name: t("breadcrumb.courseGroup"),
      child_screens: [
        { name: t("breadcrumb.groups"), path: "/teams", isQuery: false },
        {
          name: t("breadcrumb.courseGroup"),
          path: "/course-group",
          isQuery: false,
        },
      ],
    },
    {
      screen_name: t("breadcrumb.privilegeGroup"),
      child_screens: [
        { name: t("breadcrumb.groups"), path: "/teams", isQuery: false },
        {
          name: t("breadcrumb.privilegeGroup"),
          path: "/group-privileges",
          isQuery: false,
        },
      ],
    },
    {
      screen_name: t("breadcrumb.accessPrivileges"),
      child_screens: [
        {
          name: t("breadcrumb.accessPrivileges"),
          path: "/privileges",
          isQuery: false,
        },
      ],
    },
    {
      screen_name: t("breadcrumb.currentAffairs"),
      child_screens: [
        {
          name: t("breadcrumb.currentAffairs"),
          path: "/current-affairs",
          isQuery: false,
        },
      ],
    },
    {
      screen_name: t("breadcrumb.addCurrentAffairs"),
      child_screens: [
        {
          name: t("breadcrumb.currentAffairs"),
          path: "/current-affairs",
          isQuery: false,
        },
        {
          name: t("breadcrumb.addCurrentAffairs"),
          path: "/add-current-affairs",
          isQuery: false,
        },
      ],
    },
    {
      screen_name: t("breadcrumb.subscriptionPlans"),
      child_screens: [
        {
          name: t("breadcrumb.subscriptionPlans"),
          path: "/membership-plan-list",
          isQuery: false,
        },
      ],
    },
    {
      screen_name: t("breadcrumb.addSubscriptionPlan"),
      child_screens: [
        {
          name: t("breadcrumb.addSubscriptionPlan"),
          path: "/membership-plan-list",
          isQuery: false,
        },
      ],
    },
    {
      screen_name: t("breadcrumb.editSubscriptionPlan"),
      child_screens: [
        {
          name: t("breadcrumb.editSubscriptionPlan"),
          path: "/membership-plan-list",
          isQuery: false,
        },
      ],
    },
    {
      screen_name: t("breadcrumb.updateSubscriptionPlan"),
      child_screens: [
        {
          name: t("breadcrumb.subscriptionPlans"),
          path: "/membership-plan-list",
          isQuery: false,
        },
        {
          name: t("breadcrumb.updateSubscriptionPlan"),
          path: "/course-resource-link",
          isQuery: false,
        },
      ],
    },
    {
      screen_name: t("breadcrumb.resources"),
      child_screens: [
        {
          name: t("breadcrumb.resources"),
          path: "/resources-library",
          isQuery: false,
        },
        // { name: "Update Resource to the Subscription", path: "/course-resource-link", isQuery: false },
      ],
    },
    {
      screen_name: t("breadcrumb.youtubeVideos"),
      child_screens: [
        {
          name: t("breadcrumb.youtubeVideos"),
          path: "/youtube-videos",
          isQuery: false,
        },
        // { name: "Update Resource to the Subscription", path: "/course-resource-link", isQuery: false },
      ],
    },
    {
      screen_name: t("breadcrumb.activityLog"),
      child_screens: [
        {
          name: t("breadcrumb.activityLog"),
          path: "/activity-logs",
          isQuery: false,
        },
        // { name: "Update Resource to the Subscription", path: "/course-resource-link", isQuery: false },
      ],
    },
    {
      screen_name: t("breadcrumb.usersList"),
      child_screens: [
        {
          name: t("breadcrumb.usersList"),
          path: "/assign-organization",
          isQuery: false,
        },
        // { name: "Update Resource to the Subscription", path: "/course-resource-link", isQuery: false },
      ],
    },
    {
      screen_name: t("breadcrumb.configuration"),
      child_screens: [
        {
          name: t("breadcrumb.configuration"),
          path: "/config",
          isQuery: false,
        },
        // { name: "Add Exams", path: "/add-exams", isQuery: false },
      ],
    },
    {
      screen_name: t("breadcrumb.scheduleMeeting"),
      child_screens: [
        {
          name: t("breadcrumb.scheduleMeeting"),
          path: "/scheduleMeeting",
          isQuery: false,
        },
        // { name: "Add Exams", path: "/add-exams", isQuery: false },
      ],
    },
    {
      screen_name: t("breadcrumb.meetingList"),
      child_screens: [
        {
          name: t("breadcrumb.meetingList"),
          path: "/meeting-list",
          isQuery: false,
        },
        // { name: "Add Exams", path: "/add-exams", isQuery: false },
      ],
    },
  ];

  const breadcrumb = breadcrumbItems.find(
    (item) => item.screen_name === screenName,
  );

  if (breadcrumb) {
    breadcrumb.child_screens.forEach((item) => {
      if (item.isQuery && Object.keys(query).length > 0) {
        const params = new URLSearchParams(
          Object.entries(query).map(([key, value]) => [key, String(value)]),
        );
        item.path = `${item.path}?${params.toString()}`;
      }
    });
    return [...commonItems, ...breadcrumb.child_screens];
  } else {
    return [];
  }
};

export default getBreadCrumbItems;
