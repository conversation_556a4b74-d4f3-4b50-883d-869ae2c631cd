"use client";
import { useEffect, useState } from "react";
import MainLayout from "../layout/mainlayout";
import { Spinner } from "@/components/ui/progressiveLoader";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import { LiveClassSchema } from "@/schema/schema";
import type {
  ComboData,
  Enrollment,
  ErrorCatch,
  InnerItem,
  insertMessageRequest,
  LiveClassSchemaType,
  LogUserActivityRequest,
  ToastType,
} from "@/types";

import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import { DateTimePicker } from "@/components/ui/date-time-picker/date-time-picker";
import type { DateValue } from "@internationalized/date";
import { Combobox } from "@/components/ui/combobox";
import useCourse from "@/hooks/useCourse";
import useLiveClass from "@/hooks/useLiveClass";
import {
  DEFAULT_FOLDER_ID,
  formatDate,
  MEETING_PLATFORM,
  ORG_KEY,
} from "@/lib/constants";
import { useToast } from "@/components/ui/use-toast";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumb";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import useEnrollments from "@/hooks/useEnrollment";
import { useTranslation } from "react-i18next";

export default function ZoomMeeting(): JSX.Element {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { publishMeetingDetails } = useLiveClass();
  const form = useForm<LiveClassSchemaType>({
    resolver: zodResolver(LiveClassSchema),
  });
  const [courseData, setCourseData] = useState<ComboData[]>([]);
  const [selectedPlatform, setSelectedPlatform] = useState<string | null>(null);
  const [courseId, setCourseId] = useState<string | null>(null);
  const { getCourseList } = useCourse();
  const { toast } = useToast() as ToastType;
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  const { updateUserActivity } = useLogUserActivity();
  const { getEnrollments, fetDeviceToken, pushNotification, insertMessage } =
    useEnrollments();
  const [data, setData] = useState<Enrollment[]>([]);

  useEffect(() => {
    setBreadcrumbItems(
      getBreadCrumbItems(t, t("breadcrumb.scheduleMeeting"), { "": "" }),
    );
    const fetchCourseData = async (): Promise<void> => {
      try {
        const courses = await getCourseList("");
        setIsLoading(true);
        if (
          courses !== null &&
          courses !== undefined &&
          Object.keys(courses).length > 0
        ) {
          const filteredCourses = courses
            .filter((course) => course.course_id != null && course.short_name)
            .map((course) => ({
              value: course.course_id,
              label: course.short_name,
            }));
          setCourseData(filteredCourses as ComboData[]);
        } else {
          setCourseData([]);
        }
        setIsLoading(false);
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err.message,
        });
      }
    };
    fetchCourseData().catch((error) => console.log(error));
  }, [t]);

  const fetchEnrollmentData = async (value: string): Promise<void> => {
    try {
      const enrollments = await getEnrollments(value as string);

      if (
        enrollments !== null &&
        enrollments !== undefined &&
        Object.keys(enrollments).length > 0
      ) {
        setData(enrollments);
      } else {
        setData([]);
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
    }
  };
  const insertMessageToTable = async (
    userId: string,
    token: string,
    msg: string,
  ): Promise<void> => {
    const orgId = localStorage.getItem("orgId");
    const reqParams: insertMessageRequest = {
      org_id: orgId as string,
      user_id: userId,
      notification_data: {
        target_id: null,
        device_token_id: token,
        message_text: msg,
      },
    };

    try {
      const resp = await insertMessage(reqParams);
      console.log("Message inserted:", resp);
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err.message,
      });
      console.log(err);
    }
  };

  const onSubmit = async (): Promise<void> => {
    const orgId = localStorage.getItem(ORG_KEY);
    const formData = form.getValues();

    if (
      (formData.meeting_id == null || formData.meeting_id.trim() === "") &&
      (formData.meeting_url == null || formData.meeting_url.trim() === "")
    ) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.missingUrl"),
      });
      return;
    }
    const meetingId = formData.meeting_id ?? null;
    const meetingUrl = formData.meeting_url ?? null;
    const params = {
      org_id: orgId as string,
      course_id: courseId,
      meeting_id: meetingId?.trim() !== "" ? meetingId : null,
      meeting_url: meetingUrl?.trim() !== "" ? meetingUrl : null,
      meeting_type: formData.meeting_platform,
      passcode: formData.meeting_platform === "zoom" ? formData.passcode : null,
      start_date: new Date(
        formData.start_date.year,
        formData.start_date.month - 1,
        formData.start_date.day,
        formData.start_date.hour ?? 0,
        formData.start_date.minute ?? 0,
      ).toISOString(),
      end_date: new Date(
        formData.end_date.year,
        formData.end_date.month - 1,
        formData.end_date.day,
        formData.end_date.hour ?? 0,
        formData.end_date.minute ?? 0,
      ).toISOString(),
    };

    try {
      const result = await publishMeetingDetails(params);
      if (result.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.toast_success_title"),
          description: t("successMessages.publish_meeting_details"),
        });
        const param = {
          activity_type: "Live_Class",
          screen_name: "Live Class",
          action_details: "Meeting Created ",
          target_id: result.live_class_id,
          log_result: "SUCCESS",
        };
        void updateLogUserActivity(param).catch((error) => {
          console.error(error);
        });

        // Send notification to each enrolled user
        for (const item of data) {
          const userId = item.id;
          const tokenParams = {
            org_id: orgId as string,
            user_id: userId,
          };

          try {
            const msg = `You have a new meeting scheduled from ${formatDate(
              params.start_date,
            )} to ${formatDate(params.end_date)}`;
            const resp = await fetDeviceToken(tokenParams);
            const token = resp.result[0];
            const reqParams = {
              userid: userId,
              message: msg,
              fcmtoken: token,
            };
            if (resp.result.length > 0) {
              const resp = await pushNotification(reqParams);
              if (resp.message === "Notification sent successfully") {
                void insertMessageToTable(userId, token, msg);
              }
            }
          } catch (error) {
            const err = error as ErrorCatch;
            toast({
              variant: ERROR_MESSAGES.toast_variant_destructive,
              title: t("errorMessages.toast_error_title"),
              description: err.message,
            });
            console.log(err);
          }
        }
      } else if (result.status === "error") {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: t("errorMessages.publish_meeting_details"),
        });
        const params = {
          activity_type: "Live_Class",
          screen_name: "Live Class",
          action_details: "Failed To Create Meeting ",
          target_id: DEFAULT_FOLDER_ID,
          log_result: "ERROR",
        };
        void updateLogUserActivity(params).catch((error) => {
          console.error(error);
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: err?.message,
      });
      const params = {
        activity_type: "Live_Class",
        screen_name: "Live Class",
        action_details: "Failed To Create Meeting ",
        target_id: DEFAULT_FOLDER_ID,
        log_result: "ERROR",
      };
      void updateLogUserActivity(params).catch((error) => {
        console.error(error);
      });
    }
  };
  function handleCourseChange(value: string): void {
    setCourseId(value);
  }
  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  const handleInputChange =
    (fieldName: "meeting_url" | "meeting_id") =>
    (e: React.ChangeEvent<HTMLInputElement>): void => {
      const value = e.target.value;
      const sanitizedValue = value.trimStart();

      form.setValue(fieldName, sanitizedValue);
    };

  return (
    <MainLayout>
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <span>
        <h1 className="text-2xl font-semibold ">{t("meeting.title")}</h1>
      </span>
      <Form {...form}>
        <form onSubmit={(event) => void form.handleSubmit(onSubmit)(event)}>
          <div className="p-4 md:p-6 ">
            <div className=" border rounded-md p-4 mt-4 bg-[#fff]">
              <div className="p-6 ">
                <div className="mb-8">
                  <h2 className="text-lg font-medium text-black mb-4">
                    {t("meeting.meetingDetails")}
                  </h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5">
                    <div className="md:col-span-2">
                      <FormField
                        name="course_id"
                        control={form.control}
                        render={({ field }) => (
                          <FormItem>
                            <>
                              <FormLabel>{t("meeting.selectCourse")}</FormLabel>
                            </>
                            <>
                              <FormControl>
                                <Combobox
                                  data={courseData}
                                  onSelectChange={(value) => {
                                    field.onChange(value);
                                    handleCourseChange(value);
                                    void fetchEnrollmentData(value);
                                  }}
                                />
                              </FormControl>
                            </>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    <div className="md:col-span-2">
                      <FormField
                        name="meeting_platform"
                        control={form.control}
                        render={({ field }) => (
                          <FormItem>
                            <>
                              <FormLabel>
                                {t("meeting.selectMeetingPlatform")}
                                <span className="text-red-500 ml-1">*</span>
                              </FormLabel>
                            </>
                            <>
                              <FormControl>
                                <Combobox
                                  data={MEETING_PLATFORM}
                                  onSelectChange={(value) => {
                                    field.onChange(value);
                                    setSelectedPlatform(value);
                                    if (value === "gmeet") {
                                      form.setValue("passcode", "000000");
                                    }
                                    if (value === "zoom") {
                                      form.setValue("passcode", "");
                                    }
                                  }}
                                />
                              </FormControl>
                            </>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  {/* Meeting Access Information Section */}
                  <div className="mt-6">
                    <h3 className="text-lg font-medium text-black mb-3">
                      {t("meeting.accessInfo")}
                      <span className="text-red-500 ml-1">*</span>
                    </h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5">
                      <div className="md:col-span-2">
                        <FormField
                          control={form.control}
                          name="meeting_url"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-black font-medium">
                                {t("meeting.meetingUrl")}
                              </FormLabel>
                              <FormControl>
                                <Input
                                  {...field}
                                  autoComplete="off"
                                  maxLength={50}
                                  placeholder=""
                                  onChange={handleInputChange("meeting_url")}
                                  className="border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 rounded-lg"
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="md:col-span-2">
                        <FormField
                          control={form.control}
                          name="meeting_id"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-black font-medium">
                                {t("meeting.meetingId")}
                              </FormLabel>
                              <FormControl>
                                <Input
                                  {...field}
                                  autoComplete="off"
                                  maxLength={50}
                                  placeholder=""
                                  onChange={handleInputChange("meeting_id")}
                                  className="border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 rounded-lg"
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {selectedPlatform === "zoom" && (
                        <div className="md:col-span-2">
                          <FormField
                            control={form.control}
                            name="passcode"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-black font-medium">
                                  {t("meeting.passcode")}
                                  <span className="text-red-500 ml-1">*</span>
                                </FormLabel>
                                <FormControl>
                                  <Input
                                    {...field}
                                    autoComplete="off"
                                    maxLength={20}
                                    placeholder=""
                                    className="border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 rounded-lg"
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="mb-8">
                  <h2 className="text-lg font-medium text-black mb-4">
                    {t("meeting.schedule")}
                  </h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                    <div>
                      <FormField
                        control={form.control}
                        name="start_date"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-black font-medium">
                              {t("meeting.startDateTime")}
                              <span className="text-red-500 ml-1">*</span>
                            </FormLabel>
                            <FormControl>
                              <div className="border border-gray-300 rounded-lg overflow-hidden">
                                <DateTimePicker
                                  granularity={"minute"}
                                  hideTimeZone={true}
                                  value={field.value as DateValue}
                                  onChange={(newDate) => {
                                    field.onChange(newDate);
                                  }}
                                />
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    <div>
                      <FormField
                        control={form.control}
                        name="end_date"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-black font-medium">
                              {t("meeting.endDateTime")}
                              <span className="text-red-500 ml-1">*</span>
                            </FormLabel>
                            <FormControl>
                              <div className="border border-gray-300 rounded-lg overflow-hidden">
                                <DateTimePicker
                                  granularity={"minute"}
                                  hideTimeZone={true}
                                  value={field.value as DateValue}
                                  onChange={(newDate) => {
                                    field.onChange(newDate);
                                  }}
                                />
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                </div>
                <div className="pt-6 mb-6">
                  <div className="flex justify-end">
                    <Button type="submit" className="bg-[#9FC089]">
                      {t("buttons.submit")}
                    </Button>
                  </div>
                </div>
                {isLoading && (
                  <div className="flex justify-center my-6">
                    <Spinner></Spinner>
                  </div>
                )}
              </div>
            </div>
          </div>
        </form>
      </Form>
    </MainLayout>
  );
}
