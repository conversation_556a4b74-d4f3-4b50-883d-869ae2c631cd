/**
 * Utility functions for handling Google Drive URLs
 */

/**
 * Converts a Google Drive sharing URL to a direct view/download URL
 * @param url - The Google Drive sharing URL
 * @returns The converted URL for direct viewing
 */
export const convertGoogleDriveUrl = (url: string): string => {
  if (!url.includes('drive.google.com')) {
    return url; // Return as-is if not a Google Drive URL
  }

  try {
    // Extract file ID from various Google Drive URL formats
    let fileId = '';
    
    // Format 1: https://drive.google.com/file/d/FILE_ID/view?usp=sharing
    const viewMatch = url.match(/\/file\/d\/([a-zA-Z0-9-_]+)/);
    if (viewMatch) {
      fileId = viewMatch[1];
    }
    
    // Format 2: https://drive.google.com/open?id=FILE_ID
    const openMatch = url.match(/[?&]id=([a-zA-Z0-9-_]+)/);
    if (openMatch) {
      fileId = openMatch[1];
    }
    
    if (!fileId) {
      console.warn('Could not extract file ID from Google Drive URL:', url);
      return url;
    }
    
    // Return direct view URL
    return `https://drive.google.com/file/d/${fileId}/preview`;
  } catch (error) {
    console.error('Error converting Google Drive URL:', error);
    return url;
  }
};

/**
 * Converts Google Drive URL to direct download URL
 * @param url - The Google Drive sharing URL
 * @returns The converted URL for direct download
 */
export const convertGoogleDriveToDownload = (url: string): string => {
  if (!url.includes('drive.google.com')) {
    return url;
  }

  try {
    let fileId = '';
    
    const viewMatch = url.match(/\/file\/d\/([a-zA-Z0-9-_]+)/);
    if (viewMatch) {
      fileId = viewMatch[1];
    }
    
    const openMatch = url.match(/[?&]id=([a-zA-Z0-9-_]+)/);
    if (openMatch) {
      fileId = openMatch[1];
    }
    
    if (!fileId) {
      return url;
    }
    
    // Return direct download URL
    return `https://drive.google.com/uc?export=download&id=${fileId}`;
  } catch (error) {
    console.error('Error converting Google Drive URL to download:', error);
    return url;
  }
};

/**
 * Checks if a URL is a Google Drive URL
 * @param url - The URL to check
 * @returns True if it's a Google Drive URL
 */
export const isGoogleDriveUrl = (url: string): boolean => {
  return url.includes('drive.google.com') || url.includes('docs.google.com');
};

/**
 * Gets the appropriate viewer URL for different document types
 * @param url - The original URL
 * @param fileType - The type of file (pdf, doc, etc.)
 * @returns The appropriate viewer URL
 */
export const getViewerUrl = (url: string, fileType: string): string => {
  if (isGoogleDriveUrl(url)) {
    if (fileType === 'pdf') {
      return convertGoogleDriveUrl(url);
    }
    // For other document types, use Google Docs viewer
    return `https://docs.google.com/viewer?url=${encodeURIComponent(convertGoogleDriveToDownload(url))}&embedded=true`;
  }
  
  return url;
};
