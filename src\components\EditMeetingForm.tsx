"use client";
import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useTranslation } from "react-i18next";
import type { LiveClassResponse, LiveClassUpdateRequest } from "@/types";
import useLiveClass from "@/hooks/useLiveClass";
import { id } from "date-fns/locale";
import moment from "moment";

interface EditMeetingFormProps {
  meeting: LiveClassResponse;
  onSave: (updatedMeeting: LiveClassResponse) => void;
  onCancel: () => void;
}

export const EditMeetingForm: React.FC<EditMeetingFormProps> = ({
  meeting,
  onSave,
  onCancel,
}) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    id:meeting.live_class_id,
    meeting_id: meeting.meeting_id || "",
    meeting_url: meeting.meeting_url || "",
    passcode: meeting.passcode || "",
    start_date: meeting.start_date ? new Date(meeting.start_date).toISOString().slice(0, 16) : "",
    end_date: meeting.end_date ? new Date(meeting.end_date).toISOString().slice(0, 16) : "",
    meeting_type: meeting.meeting_type || "",
  });

  const [isLoading, setIsLoading] = useState(false);
  const {updateLiveClassDetails} = useLiveClass();
  const handleInputChange = (field: string, value: string): void => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Convert datetime-local format back to ISO string
      const updatedMeeting: LiveClassResponse = {
        
        id: meeting.live_class_id,
         meeting_id: formData.meeting_id,
        meeting_url: formData.meeting_url,
        passcode: formData.meeting_type === "zoom" ? formData.passcode : "",
        valid_from:  moment
                  .utc(formData.start_date)
                  .local()
                  .format("DD-MMM-YYYY hh:mm a"),
        valid_upto: moment
                  .utc(formData.end_date)
                  .local()
                  .format("DD-MMM-YYYY hh:mm a")
       
      };
      
      const rep  = await updateLiveClassDetails(updatedMeeting);
      // onSave(updatedMeeting);
    } catch (error) {
      console.error("Error updating meeting:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="meeting_id">{t("meeting.meetingId")}</Label>
          <Input
            id="meeting_id"
            value={formData.meeting_id}
            onChange={(e) => handleInputChange("meeting_id", e.target.value)}
            placeholder={t("meeting.enterMeetingId")}
          />
        </div>

        <div>
          <Label htmlFor="meeting_type">{t("meeting.platform")}</Label>
          <Input
            id="meeting_type"
            value={formData.meeting_type === "zoom" ? "Zoom" : formData.meeting_type === "meet" ? "Google Meet" : formData.meeting_type}
            readOnly
            disabled
            className="bg-gray-100 cursor-not-allowed"
            placeholder={t("meeting.platform")}
          />
        </div>

        <div className="md:col-span-2">
          <Label htmlFor="meeting_url">{t("meeting.meetingUrl")}</Label>
          <Input
            id="meeting_url"
            value={formData.meeting_url}
            onChange={(e) => handleInputChange("meeting_url", e.target.value)}
            placeholder={t("meeting.enterMeetingUrl")}
            type="url"
          />
        </div>

        {formData.meeting_type === "zoom" && (
          <div>
            <Label htmlFor="passcode">{t("meeting.passcode")}</Label>
            <Input
              id="passcode"
              value={formData.passcode}
              onChange={(e) => handleInputChange("passcode", e.target.value)}
              placeholder={t("meeting.enterPasscode")}
            />
          </div>
        )}

        <div>
          <Label htmlFor="start_date">{t("meeting.startDateTime")}</Label>
          <Input
            id="start_date"
            type="datetime-local"
            value={formData.start_date}
            onChange={(e) => handleInputChange("start_date", e.target.value)}
          />
        </div>

        <div>
          <Label htmlFor="end_date">{t("meeting.endDateTime")}</Label>
          <Input
            id="end_date"
            type="datetime-local"
            value={formData.end_date}
            onChange={(e) => handleInputChange("end_date", e.target.value)}
          />
        </div>
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
        >
          {t("buttons.cancel")}
        </Button>
        <Button
          type="submit"
          disabled={isLoading}
          className="bg-[#9FC089] hover:bg-[#8FB079]"
        >
          {isLoading ? t("buttons.saving") : t("buttons.save")}
        </Button>
      </div>
    </form>
  );
};
