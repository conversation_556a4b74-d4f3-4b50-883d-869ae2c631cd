"use client";
import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useTranslation } from "react-i18next";
import { DateTimePicker } from "@/components/ui/date-time-picker/date-time-picker";
import {
  type ZonedDateTime,
  parseZonedDateTime,
} from "@internationalized/date";
import type { DateValue } from "react-aria";
import moment from "moment-timezone";
import type { LiveClassResponse } from "@/types";

interface EditMeetingFormProps {
  meeting: LiveClassResponse;
  onSave: (updatedMeeting: LiveClassResponse) => void;
  onCancel: () => void;
}

export const EditMeetingForm: React.FC<EditMeetingFormProps> = ({
  meeting,
  onSave,
  onCancel,
}) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    meeting_id: meeting.meeting_id || "",
    meeting_url: meeting.meeting_url || "",
    passcode: meeting.passcode || "",
    meeting_type: meeting.meeting_type || "",
  });

  // State for DateTimePicker
  const [startDateTime, setStartDateTime] = useState<ZonedDateTime | DateValue>();
  const [endDateTime, setEndDateTime] = useState<ZonedDateTime | DateValue>();
  const [isLoading, setIsLoading] = useState(false);

  // Initialize DateTimePicker values
  useEffect(() => {
    if (meeting.start_date && meeting.start_date !== "") {
      const currentTimezone = moment.tz.guess();
      const parsedStartDate = moment.tz(meeting.start_date, currentTimezone);
      const formattedStartDate = parseZonedDateTime(
        parsedStartDate.format("YYYY-MM-DDTHH:mm") + `[${currentTimezone}]`,
      );
      setStartDateTime(formattedStartDate);
    }

    if (meeting.end_date && meeting.end_date !== "") {
      const currentTimezone = moment.tz.guess();
      const parsedEndDate = moment.tz(meeting.end_date, currentTimezone);
      const formattedEndDate = parseZonedDateTime(
        parsedEndDate.format("YYYY-MM-DDTHH:mm") + `[${currentTimezone}]`,
      );
      setEndDateTime(formattedEndDate);
    }
  }, [meeting]);
  const handleInputChange = (field: string, value: string): void => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent): Promise<void> => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Convert DateTimePicker values to ISO strings
      const updatedMeeting: LiveClassResponse = {
        ...meeting,
        meeting_id: formData.meeting_id,
        meeting_url: formData.meeting_url,
        passcode: formData.meeting_type === "zoom" ? formData.passcode : "",
        start_date: startDateTime ? new Date(startDateTime.toString()).toISOString() : meeting.start_date,
        end_date: endDateTime ? new Date(endDateTime.toString()).toISOString() : meeting.end_date,
        meeting_type: formData.meeting_type,
      };

      // Here you would typically make an API call to update the meeting
      // For now, we'll just call the onSave callback
      onSave(updatedMeeting);
    } catch (error) {
      console.error("Error updating meeting:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="meeting_id">{t("meeting.meetingId")}</Label>
          <Input
            id="meeting_id"
            value={formData.meeting_id}
            onChange={(e) => handleInputChange("meeting_id", e.target.value)}
            placeholder={t("meeting.enterMeetingId")}
          />
        </div>

        <div>
          <Label htmlFor="meeting_type">{t("meeting.platform")}</Label>
          <Input
            id="meeting_type"
            value={formData.meeting_type === "zoom" ? "Zoom" : formData.meeting_type === "meet" ? "Google Meet" : formData.meeting_type}
            readOnly
            disabled
            className="bg-gray-100 cursor-not-allowed"
            placeholder={t("meeting.platform")}
          />
        </div>

        <div className="md:col-span-2">
          <Label htmlFor="meeting_url">{t("meeting.meetingUrl")}</Label>
          <Input
            id="meeting_url"
            value={formData.meeting_url}
            onChange={(e) => handleInputChange("meeting_url", e.target.value)}
            placeholder={t("meeting.enterMeetingUrl")}
            type="url"
          />
        </div>

        {formData.meeting_type === "zoom" && (
          <div>
            <Label htmlFor="passcode">{t("meeting.passcode")}</Label>
            <Input
              id="passcode"
              value={formData.passcode}
              onChange={(e) => handleInputChange("passcode", e.target.value)}
              placeholder={t("meeting.enterPasscode")}
            />
          </div>
        )}

        <div>
          <Label htmlFor="start_date">{t("meeting.startDateTime")}</Label>
          <DateTimePicker
            granularity="minute"
            value={startDateTime as DateValue}
            hideTimeZone={true}
            onChange={(newDate) => {
              setStartDateTime(newDate);
            }}
          />
        </div>

        <div>
          <Label htmlFor="end_date">{t("meeting.endDateTime")}</Label>
          <DateTimePicker
            granularity="minute"
            minValue={startDateTime as DateValue}
            value={endDateTime as DateValue}
            hideTimeZone={true}
            onChange={(newDate) => {
              setEndDateTime(newDate);
            }}
          />
        </div>
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
        >
          {t("buttons.cancel")}
        </Button>
        <Button
          type="submit"
          disabled={isLoading}
          className="bg-[#9FC089] hover:bg-[#8FB079]"
        >
          {isLoading ? t("buttons.saving") : t("buttons.save")}
        </Button>
      </div>
    </form>
  );
};
